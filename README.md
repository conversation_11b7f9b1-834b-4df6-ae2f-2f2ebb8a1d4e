# 数据清洗智能化改造升级

## 背景

### 现状

平台能力提升与创新方案实施

用户常用清洗算子：

1. 默认值替换
2. 正则表达式匹配丢弃
3. 去重
4. 敏感信息替换
5. 按字段过滤
6. HTML清除
7. 空格标准化

### 痛点

1. 用户偏向使用基础清洗算子，对于更多算子不清楚、不知如何使用或不知该不该使用。
2. 数据与清洗操作分离，大部分是基于单个问题独立清洗，而不能同时查看分析数据，同时完善清洗pipeline。
3. 算子使用专业性要求过高，导致一般用户无法按需使用，比如需要编写适合的正则表达式。

### 限制

1. 清洗过程依赖较高硬件资源，不适合类MCP工具的实时调用
2. 清洗全流程操作MCP化接入工作量较大，影响功能范围较多
3. 大部分清洗流程有较为固定的工作流和模式，Agent帮助有限

## 方案

数据画像（检查） -> 数据洞察（诊断） -> 算子编排（处方） -> 基于agent的清洗算子辅助增强

- 1阶段：数据画像 -> 数据洞察 -> 算子编排
- 2阶段：基于agent的清洗算子增强

### 数据画像（检查）

![数据画像主流程](assets/数据画像主.png)

- 输入为jsonl格式数据 + 待分析字段
- 分析范围是全部样本
- 分析主体是样本的字段

**统计分析**

*第一类：基础文本结构统计*
- 词数分布
- 句子数分布
- 行数分布
- 平均行长度分布
- 平均句子长度分布
- 平均词长度分布
- 无效文本比例

*第二类：字符级别统计与编码检测*
- 样本字符数分布
- 样本特殊字符占比分布
- 样本字母数字占比分布
- 非标准空格编码识别
- 不可见字符识别
- 编码问题识别

*第三类：重复度检测统计*
- 样本n-gram重复比例分布
- Type-Token Ratio
- Measure of Textual Lexical Diversity(MTLD)

**内容分析**

*第一类：格式标记识别（正则表达式模式匹配）*
- HTML标签识别
- URL链接识别

*第二类：外部词表依赖分析*
- 样本语言分布（需要语言检测模型/词典）
- 样本敏感词占比分布（需要敏感词词典）
- 样本停用词占比分布（需要停用词词典）

*第三类：敏感信息识别（正则表达式模式匹配）*
- 敏感信息识别（PII: IP、邮箱、电话、手机、地址、身份证号、银行卡号、传真号码）

### 分析报告（诊断）

根据统计指标筛选异常项目，并结合用户数据内容的实际需求，分析重点数据质量问题。

### 算子编排（处方）

根据预设清洗经验，以及分析报告的重点质量问题，推荐清洗算子和参数配置。

### 基于agent的清洗算子辅助增强

**正则编写辅助**

    对于想要自定义清洗样本中某些文本片段的用户，可以使用正则表达式进行清洗。通过该模块可以辅助用户生成合适的正则表达式，自动编译和测试。

**指令编写辅助**

    对于想要增加或优化固定系统指令的用户，可以使用该模块辅助生成合适的指令，结合实际数据内容和用户需求理解，生成更准确更高质量的系统指令。

**统计指标解答**

    帮助用户了解数据画像中指标的含义，解答用户数据集中指标存在的问题。

**算子功能说明**

    帮助用户了解算子的功能和使用方法，解答用户在使用算子过程中遇到的问题。

### 功能集成设计

1. 新增数据分析（数据画像）Tab页，提供数据画像分析：数据分析（模型）、统计分析（统计）
2. 数据分析Tab页对应的数据集分析详情下提供“算子编排推荐”按钮，点击后再数据清洗tab新增任务，显示“编排中”
3. 编排成功后，打开该清洗任务，在可编辑算子配置界面展示推荐的算子编排与默认配置
4. 数据清洗的数据详情页，新增对话组件、数据预览组件

### 原型界面设计

**数据画像**

箱图指标

占比指标

**数据洞察**

| 数据字段 | 指标名称 | 指标值 | 数据洞察 |
|---------|---------|--------|----------|
| content | 字数分布 | 156字 | ✅ 正常：文本长度适中，符合预期范围 |
|  | 句子数分布 | 3句 | ⚠️ 注意：句子数偏少，可能影响语义完整性 |
|  | 平均句子长度分布 | 52字/句 | ❌ 异常：句子过长，建议进行句子分割处理 |
|  | 有效字符占比分布 | 94.2% | ✅ 正常：有效字符占比良好 |
|  | 2-gram重复度分布 | 15.3% | ⚠️ 注意：存在一定重复，建议检查去重策略 |
|  | 包含特殊字符样本占比 | 23.5% | ⚠️ 注意：特殊字符较多，可能需要标准化处理 |
|  | 包含HTML标记样本占比 | 8.7% | ❌ 异常：检测到HTML标签，建议进行HTML清除 |
|  | 包含PII信息样本占比 | 12.1% | 🔴 风险：检测到个人敏感信息，需要脱敏处理 |
|  | 包含敏感词样本占比 | 2.3% | 🔴 风险：存在敏感词汇，需要过滤或替换 |
|  | 编码类型占比 | UTF-8: 89.2% | ✅ 正常：编码统一性良好 |
| title | 字数分布 | 12字 | ✅ 正常：标题长度合适 |
|  | 包含特殊字符样本占比 | 5.2% | ✅ 正常：特殊字符占比较低 |

**洞察总结**
- 🔴 **高风险问题**：检测到PII信息和敏感词，需要优先处理
- ❌ **数据质量问题**：句子过长、HTML标记需要清理
- ⚠️ **优化建议**：句子分割、去重、特殊字符标准化
- ✅ **良好指标**：文本长度、编码统一性、有效字符占比

**推荐处理顺序**
1. 敏感信息脱敏 → 2. HTML标签清除 → 3. 句子分割 → 4. 去重处理 → 5. 特殊字符标准化


## 实施

### 数据画像

#### 指标构建流程

第一步：对所有样本进行语言类型标记

第二步：对于中文、英文进行不同的指标统计

第三步：输出数据画像数据结构

#### 可视化设计与数据支持

中文（占比x.x%）：

**箱图展示指标**

![箱图指标示例](assets/箱图指标.png)

- 字数分布
- 句子数分布
- 行数分布
- 平均句子长度分布
- 平均词长度分布
- 平均行长度分布
- 有效字符占比分布（无效字符：特殊字符、不可见字符、控制字符等）
- 2-gram重复度分布
- 3-gram重复度分布

数据支持：

    1.5×IQR 异常值
    3×IQR 极端异常值
    异常值比例
    Q1/Q3/中位数/上下须线/异常值比例/偏度峰度

**占比统计指标**

![占比指标示例](assets/占比指标.png)

- 无效文本占比（无效文本/总文本）
- 编码类型占比（GBK、UTF-8、Latin-1、其他）
- 包含特殊字符样本占比
- 包含非标准空格样本占比
- 包含不可见字符样本占比
- 包含编码问题样本占比
- 包含敏感词样本占比
- 包含PII信息样本占比
- 包含HTML标记样本占比
- 包含网络协议地址样本占比
- 停用词高占比样本比例
- 数字高占比样本比例

数据支持：

    占比、包含问题文本片段

---

英文（占比x.x%）：

**箱图展示指标**

- 字符数分布
- 词数分布：
- 句子数分布
- 行数分布
- 平均行长度分布
- 平均句子长度分布
- 平均词长度分布
- 有效字符占比分布（无效字符：特殊字符、不可见字符、控制字符等）
- 样本字母数字占比分布
- 2-gram重复度分布
- 3-gram重复度分布
- Measure of Textual Lexical Diversity(MTLD)分布

数据支持：

    1.5×IQR 异常值
    3×IQR 极端异常值
    异常值比例
    Q1/Q3/中位数/上下须线/异常值比例/偏度峰度

**占比统计指标**

- 无效文本占比（无效文本/总文本）
- 编码类型占比（GBK、UTF-8、Latin-1、其他）
- 包含特殊字符样本占比
- 包含非标准空格样本占比
- 包含不可见字符样本占比
- 包含编码问题样本占比
- 包含敏感词样本占比
- 包含PII信息样本占比
- 包含HTML标记样本占比
- 包含网络协议地址样本占比
- 停用词高占比样本比例
- 字母数字高占比样本比例

数据支持：

    占比、包含问题文本片段

### 数据洞察

1. 异常检测：针对每项指标设计对应的基于规则或基于模型的异常检测分析。
    - 一般项规则处理
    - 需要模型进一步分析的指标进行打包

2. 异常评估：针对存在异常的指标，在模型训练方面带来的影响进行评估。
    - 一般项规则处理
    - 需要模型进一步分析的异常进行打包

3. 风险评估：对于可能存在的较为严重的合格风险进行提示。
    - 匹配到的敏感词、风险词，基于规则提示

4. 领域洞察：分析数据集所属领域，提出针对性质量提升建议。
    - 获取质量分析的指令任务类型
    - 综合上述打包项进行模型分析

### 算子编排

1. 基于数据画像、数据洞察结论进行算子编排推荐
2. 基于数据集匹配相似数据集的算子编排作为参考进行算子编排推荐

---

## 数据分析流水线使用指南

### 快速开始

本项目提供了一个完整的数据分析流水线，实现从数据集加载、数据特征抽取、数据集画像报告生成到数据洞察分析的完整流程。

#### 运行主程序

```bash
python main.py
```

程序将自动：
1. 加载示例数据 `data/test/test_data_conversation_config_10_samples.jsonl`
2. 自动检测字符串类型字段（如：user_message, assistant_message, context等）
3. 生成数据画像报告
4. 生成数据洞察报告
5. 将报告保存到 `report/` 目录

### 功能特性

- **智能字段检测**: 自动识别数据集中的字符串类型字段，无需手动指定
- **数据画像分析**: 对文本数据进行全面的特征抽取和统计分析
- **数据洞察生成**: 基于LLM的智能数据质量分析和优化建议
- **多种数据类型支持**: 特别针对对话类型数据进行优化
- **完整报告输出**: 生成JSON格式的详细分析报告

### 自定义数据分析

如果要分析其他数据文件，可以修改 `main.py` 中的配置：

```python
def main():
    # 修改数据文件路径
    data_file = "your_data_file.jsonl"

    # 可选：手动指定分析字段
    clean_keys = ["field1", "field2", "field3"]  # 或设为None自动检测

    pipeline = DataAnalysisPipeline(
        data_file=data_file,
        clean_keys=clean_keys,  # None表示自动检测
        report_dir="report"
    )
```

### 输出报告

程序会在 `report/` 目录下生成两个JSON文件：

#### 1. 数据画像报告 (`data_profile_report_*.json`)

包含：
- **基础统计**: 样本数量、字段信息、语言分布等
- **箱图统计**: 各字段的数值分布统计（均值、中位数、四分位数等）
- **占比统计**: 分类字段的分布情况
- **模式匹配**: PII信息、敏感词、HTML标签等检测结果

#### 2. 数据洞察报告 (`data_insight_report_*.json`)

包含：
- **领域分类**: 基于LLM的数据集领域识别
- **质量评分**: 综合数据质量评分和等级
- **问题发现**: 数据质量问题的详细分析
- **优化建议**: 针对性的数据改进建议

### 支持的数据类型

程序特别针对以下数据类型进行了优化：

- **对话数据**: user_message, assistant_message, context
- **文本数据**: text, content, title, description
- **文档数据**: document, article, paragraph
- **问答数据**: question, answer, query, response

### 智能字段检测

程序会自动：
1. 跳过数值型字段（如：id, length, count等）
2. 识别有意义的文本字段
3. 按数据类型优先级排序字段
4. 过滤掉不存在的字段

### 示例输出

```
🚀 开始数据分析流程
📁 数据文件: data/test/test_data_conversation_config_10_samples.jsonl
📋 分析字段: None
📂 报告目录: report

正在加载数据集...
成功加载数据集，共 10 个样本
自动检测到的字符串字段: ['user_message', 'assistant_message', 'context', 'generated_at']

==================================================
步骤1: 生成数据画像报告
==================================================
✅ 数据画像报告生成完成

📊 数据画像报告摘要:
   总样本数: 10
   分析字段: ['user_message', 'assistant_message', 'context', 'generated_at']
   平均文本长度: 253.25 字符
   内容多样性分数: 33.61
   语言分布:
     zh: 10 样本 (100.0%)

==================================================
步骤2: 生成数据洞察报告
==================================================
✅ 数据洞察报告生成完成

🔍 数据洞察报告摘要:
   分析时间: 2025-07-21T21:59:10.715001
   总样本数: 10
   数据质量评分: 0.54/1.0
   质量等级: 一般
   发现问题: 高 0 个, 中 2 个, 低 0 个
   领域分类: 基于样本文本分析，该数据集属于客户服务及用户交互领域...

🎉 数据分析流程完成!
📄 数据画像报告: report/data_profile_report_20250721_215905.json
📄 数据洞察报告: report/data_insight_report_20250721_215905.json
```