# 数据分析流水线 - main.py 使用说明

## 概述

优化后的 `main.py` 实现了完整的数据分析流水线，包含三个核心流程：

1. **数据画像 (Profiling)** - 使用 `operators/core/profiling` 模块
2. **数据洞察 (Insight)** - 使用 `operators/core/insight` 模块  
3. **算子编排 (Orchestra)** - 使用 `operators/core/orchestra` 模块

## 核心架构

### 数据流程
```
输入数据文件 → 数据画像分析 → 洞察生成 → 算子编排 → 最终报告
```

### 核心组件
- `DataProfiler`: 提供数据特征提取的mapper方法
- `DatasetProfilerCore`: 进行数据画像统计分析
- `DataInsightCore`: 生成数据洞察报告
- `OperatorOrchestratorCore`: 进行算子编排

## 使用方法

### 命令行使用

```bash
# 基本用法
python main.py --data_file data/test.jsonl --clean_keys "user_message,assistant_message"

# 完整参数
python main.py \
    --data_file data/test.jsonl \
    --clean_keys "user_message,assistant_message,context" \
    --report_dir report \
    --result_dir result
```

### 参数说明

- `--data_file`: 输入的JSONL数据文件路径
- `--clean_keys`: 需要分析的字段列表，用逗号分隔（必需参数）
- `--report_dir`: 报告输出目录（默认: report）
- `--result_dir`: 结果输出目录（默认: result）

### 编程方式使用

```python
from main import DataAnalysisPipeline

# 创建分析流水线
pipeline = DataAnalysisPipeline(
    data_file="data/test.jsonl",
    clean_keys=["user_message", "assistant_message"],
    report_dir="report",
    result_dir="result"
)

# 运行分析
results = pipeline.run_analysis()
```

## 输出文件

分析完成后会生成以下报告文件：

1. `data_profile_report_YYYYMMDD_HHMMSS.json` - 数据画像报告
2. `data_insight_report_YYYYMMDD_HHMMSS.json` - 数据洞察报告
3. `final_analysis_report_YYYYMMDD_HHMMSS.json` - 包含算子编排的最终报告

## 示例运行

```bash
# 运行示例
python run_example.py
```

## 注意事项

1. **clean_keys 参数是必需的**：不再支持自动检测字段，必须明确指定要分析的字段
2. **数据格式**：输入文件必须是JSONL格式
3. **字段验证**：程序会验证指定的字段是否存在于数据中
4. **依赖模块**：确保所有核心模块正确导入和配置

## 错误处理

- 文件不存在：会抛出 `FileNotFoundError`
- 字段不存在：会过滤掉不存在的字段并警告
- 无有效字段：会抛出 `ValueError`
- 其他错误：会记录详细错误信息并退出

## 扩展性

该架构支持：
- 添加新的数据画像指标
- 扩展洞察分析规则
- 增加新的算子编排策略
- 自定义报告格式
