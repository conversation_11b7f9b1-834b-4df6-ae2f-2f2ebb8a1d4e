#!/usr/bin/env python3
"""
ES索引创建脚本
用于在本地ES服务中创建数据分析报告索引
"""

import json
import os
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import RequestError

def create_data_profiling_index(es_client, index_name="data_profiling_reports"):
    """
    创建数据分析报告索引

    索引结构简化为：
    - dataset_batch_id: 批次ID，用于查询
    - report_data: 完整的报告JSON数据（不索引，仅存储）
    - report_type: 报告类型，用于区分数据画像洞察报告和包含算子推荐结果的报告

    Args:
        es_client: Elasticsearch客户端实例
        index_name: 索引名称，默认为 "data_profiling_reports"

    Returns:
        bool: 创建成功返回True，否则返回False
    """
    
    # 获取mapping文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    mapping_file = os.path.join(current_dir, "data_profiling_index_mapping.json")
    
    try:
        # 读取mapping配置
        with open(mapping_file, 'r', encoding='utf-8') as f:
            index_config = json.load(f)
        
        # 检查索引是否已存在
        if es_client.indices.exists(index=index_name):
            print(f"索引 '{index_name}' 已存在")
            
            # 询问是否删除重建
            response = input("是否删除并重新创建索引? (y/N): ").strip().lower()
            if response == 'y':
                es_client.indices.delete(index=index_name)
                print(f"已删除索引 '{index_name}'")
            else:
                print("取消操作")
                return False
        
        # 创建索引
        es_client.indices.create(
            index=index_name,
            body=index_config
        )
        
        print(f"成功创建索引 '{index_name}'")
        
        # 验证索引创建
        if es_client.indices.exists(index=index_name):
            # 获取索引信息
            index_info = es_client.indices.get(index=index_name)
            print(f"索引信息: {json.dumps(index_info, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"索引创建失败: '{index_name}'")
            return False
            
    except FileNotFoundError:
        print(f"找不到mapping文件: {mapping_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"mapping文件格式错误: {e}")
        return False
    except RequestError as e:
        print(f"ES请求错误: {e}")
        return False
    except Exception as e:
        print(f"创建索引时发生错误: {e}")
        return False

def main():
    """主函数"""
    # ES连接配置
    es_config = {
        'hosts': ['localhost:9200'],
        'timeout': 30,
        'max_retries': 3,
        'retry_on_timeout': True
    }

    try:
        # 创建ES客户端
        es_client = Elasticsearch(**es_config)

        # 测试连接
        if not es_client.ping():
            print("无法连接到Elasticsearch服务")
            print("请先启动Elasticsearch服务，例如：")
            print("1. 使用Docker: docker run -d --name elasticsearch -p 9200:9200 -e 'discovery.type=single-node' -e 'xpack.security.enabled=false' elasticsearch:8.8.0")
            print("2. 或者下载并启动本地ES服务")
            print("\n索引mapping配置已准备好，ES服务启动后可重新运行此脚本")
            return False

        print("成功连接到Elasticsearch服务")

        # 创建索引
        success = create_data_profiling_index(es_client)

        if success:
            print("数据分析报告索引创建完成!")
        else:
            print("索引创建失败!")

        return success

    except Exception as e:
        print(f"连接ES服务时发生错误: {e}")
        print("请确保Elasticsearch服务正在运行在 localhost:9200")
        return False

if __name__ == "__main__":
    main()
