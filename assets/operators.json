{"operators": [{"id": "remove_special_chars", "operator_zh_name": "移除特殊字符", "operator_name": "remove_special_chars", "desc": "移除文本中的特殊字符、HTML实体、控制字符等", "parameters": [{"name": "keep_punctuation", "type": "boolean", "defaultValue": true, "desc": "是否保留标点符号"}, {"name": "custom_chars", "type": "string", "defaultValue": "", "desc": "自定义需要移除的字符"}]}, {"id": "normalize_whitespace", "operator_zh_name": "标准化空白字符", "operator_name": "normalize_whitespace", "desc": "统一空格、制表符、换行符等空白字符", "parameters": [{"name": "replace_tabs", "type": "boolean", "defaultValue": true, "desc": "是否将制表符替换为空格"}, {"name": "normalize_newlines", "type": "boolean", "defaultValue": true, "desc": "是否标准化换行符"}]}, {"id": "remove_duplicates", "operator_zh_name": "去重处理", "operator_name": "remove_duplicates", "desc": "移除重复的文本内容或样本", "parameters": [{"name": "similarity_threshold", "type": "float", "defaultValue": 0.95, "desc": "相似度阈值"}, {"name": "method", "type": "string", "defaultValue": "exact", "options": ["exact", "fuzzy", "semantic"], "desc": "去重方法"}]}, {"id": "filter_by_length", "operator_zh_name": "长度过滤", "operator_name": "filter_by_length", "desc": "根据文本长度过滤样本", "parameters": [{"name": "min_length", "type": "integer", "defaultValue": 10, "desc": "最小字符长度"}, {"name": "max_length", "type": "integer", "defaultValue": 1000, "desc": "最大字符长度"}, {"name": "unit", "type": "string", "defaultValue": "chars", "options": ["chars", "words", "sentences"], "desc": "长度单位"}]}, {"id": "language_filter", "operator_zh_name": "语言过滤", "operator_name": "language_filter", "desc": "过滤指定语言的文本", "parameters": [{"name": "target_languages", "type": "array", "defaultValue": ["zh", "en"], "desc": "目标语言列表"}, {"name": "confidence_threshold", "type": "float", "defaultValue": 0.8, "desc": "语言检测置信度阈值"}]}, {"id": "sensitive_content_filter", "operator_zh_name": "敏感内容过滤", "operator_name": "sensitive_content_filter", "desc": "过滤或脱敏敏感词汇和内容", "parameters": [{"name": "action", "type": "string", "defaultValue": "mask", "options": ["remove", "mask", "replace"], "desc": "处理方式"}, {"name": "mask_char", "type": "string", "defaultValue": "*", "desc": "掩码字符"}]}, {"id": "text_normalization", "operator_zh_name": "文本标准化", "operator_name": "text_normalization", "desc": "统一文本格式，如大小写、标点符号等", "parameters": [{"name": "lowercase", "type": "boolean", "defaultValue": false, "desc": "是否转换为小写"}, {"name": "normalize_punctuation", "type": "boolean", "defaultValue": true, "desc": "是否标准化标点符号"}]}, {"id": "outlier_removal", "operator_zh_name": "异常值移除", "operator_name": "outlier_removal", "desc": "移除统计上的异常样本", "parameters": [{"name": "method", "type": "string", "defaultValue": "iqr", "options": ["iqr", "zscore", "isolation_forest"], "desc": "异常检测方法"}, {"name": "threshold", "type": "float", "defaultValue": 1.5, "desc": "异常检测阈值"}]}, {"id": "encoding_fix", "operator_zh_name": "编码修复", "operator_name": "encoding_fix", "desc": "修复文本编码问题", "parameters": [{"name": "target_encoding", "type": "string", "defaultValue": "utf-8", "desc": "目标编码格式"}, {"name": "error_handling", "type": "string", "defaultValue": "ignore", "options": ["ignore", "replace", "strict"], "desc": "错误处理方式"}]}, {"id": "structure_validation", "operator_zh_name": "结构验证", "operator_name": "structure_validation", "desc": "验证和修复数据结构问题", "parameters": [{"name": "required_fields", "type": "array", "defaultValue": [], "desc": "必需字段列表"}, {"name": "auto_fix", "type": "boolean", "defaultValue": true, "desc": "是否自动修复"}]}]}