# 测试数据生成工具

## 概述

这是一个支持多字段定制的测试数据生成工具，特别适用于生成微调数据格式（如 system、prompt、response 等字段）。

## 功能特性

- 支持多种预定义配置模式
- 可定制字段类型和内容长度
- 支持中英文混合内容生成
- 批量生成功能
- 兼容原版本的单字段模式

## 使用方法

### 1. 单个样本生成

```bash
# 默认配置（单个text字段）
python3 generate_test_data.py 1

# 微调数据配置（system, prompt, response字段）
python3 generate_test_data.py 1 fine_tuning

# 自定义配置
python3 generate_test_data.py 1 custom
```

### 2. 批量生成

```bash
# 使用默认配置生成5000个样本
./generate_batch_test_data.sh

# 使用微调配置生成1000个样本
./generate_batch_test_data.sh -n 1000 -c fine_tuning

# 使用自定义配置生成500个样本
./generate_batch_test_data.sh -n 500 -c custom

# 查看帮助信息
./generate_batch_test_data.sh -h
```

## 配置类型

### 1. default（默认配置）
- 兼容原版本
- 单个 `text` 字段，约1000字符

### 2. fine_tuning（微调数据配置）
- `system`: 系统指令（约200字符）
- `prompt`: 用户提示（约300字符）
- `response`: AI回答（约500字符）

### 3. custom（自定义配置）
- `system`: 系统指令（约150字符）
- `user_input`: 用户输入（约200字符）
- `assistant_output`: 助手输出（约400字符）
- `metadata`: 元数据（约100字符）

## 输出格式

生成的数据为 JSONL 格式，每行一个 JSON 对象：

```json
{
  "id": 1,
  "generated_at": "sample_000001",
  "system": "你是一个专业的数据分析助手...",
  "system_length": 156,
  "prompt": "请解释一下机器学习的基本原理...",
  "prompt_length": 298,
  "response": "机器学习是人工智能领域中的重要概念...",
  "response_length": 487
}
```

## 字段类型说明

- `text`: 通用文本内容
- `system`: 系统指令，定义AI角色和行为
- `instruction`: 用户指令或问题
- `response`: AI助手的回答

## 内容类型说明

- `mixed`: 中英文混合内容
- `chinese`: 主要中文内容
- `english`: 主要英文内容
- `technical`: 技术性内容
- `instruction`: 指令性内容
- `question`: 问题性内容
- `answer`: 回答性内容

## 自定义配置

可以通过修改 `generate_test_data.py` 中的配置来定制字段：

```python
custom_config = {
    "field_name": {
        "type": "text|system|instruction|response",
        "length": 目标字符数,
        "content_type": "mixed|chinese|english|technical"
    }
}
```

## 文件说明

- `generate_test_data.py`: 核心生成脚本
- `generate_batch_test_data.sh`: 批量生成脚本
- `config_examples.json`: 配置示例文件
- `README.md`: 使用说明文档

## 示例输出

查看 `config_examples.json` 文件了解各种配置的详细示例。
