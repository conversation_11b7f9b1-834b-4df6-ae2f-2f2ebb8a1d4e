#!/bin/bash

# 批量生成测试数据脚本
# 支持多种配置模式：default, fine_tuning, custom

# 默认参数
TOTAL_SAMPLES=5000
BATCH_SIZE=100
CONFIG_TYPE="default"
CONFIG_FILE=""
CONFIG_NAME=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--samples)
            TOTAL_SAMPLES="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_TYPE="$2"
            shift 2
            ;;
        -f|--file)
            CONFIG_FILE="$2"
            CONFIG_TYPE="file"
            shift 2
            ;;
        --config-name)
            CONFIG_NAME="$2"
            shift 2
            ;;
        -b|--batch)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -n, --samples NUM       生成样本数量 (默认: 5000)"
            echo "  -c, --config TYPE       配置类型: default|fine_tuning|custom|file (默认: default)"
            echo "  -f, --file FILE         配置文件路径 (自动设置config为file)"
            echo "  --config-name NAME      配置文件中的配置名称"
            echo "  -b, --batch NUM         批次大小 (默认: 100)"
            echo "  -h, --help              显示帮助信息"
            echo ""
            echo "配置类型说明:"
            echo "  default      - 单个text字段 (兼容原版本)"
            echo "  fine_tuning  - 微调数据格式 (system, prompt, response)"
            echo "  custom       - 自定义多字段格式"
            echo "  file         - 从JSON文件加载配置"
            echo ""
            echo "示例:"
            echo "  $0 -n 1000 -c fine_tuning"
            echo "  $0 -n 500 -f config_examples.json --config-name conversation_config"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 根据配置类型设置输出文件名
if [[ "$CONFIG_TYPE" == "file" && -n "$CONFIG_NAME" ]]; then
    OUTPUT_FILE="test_data_${CONFIG_NAME}_${TOTAL_SAMPLES}_samples.jsonl"
else
    OUTPUT_FILE="test_data_${CONFIG_TYPE}_${TOTAL_SAMPLES}_samples.jsonl"
fi

echo "开始生成 $TOTAL_SAMPLES 个测试样本..."
echo "配置类型: $CONFIG_TYPE"
if [[ "$CONFIG_TYPE" == "file" ]]; then
    echo "配置文件: $CONFIG_FILE"
    [[ -n "$CONFIG_NAME" ]] && echo "配置名称: $CONFIG_NAME"
fi
echo "输出文件: $OUTPUT_FILE"
echo "批次大小: $BATCH_SIZE"
echo ""

# 清空输出文件
> "$OUTPUT_FILE"

# 记录开始时间
start_time=$(date +%s)

# 批量生成数据
for ((i=1; i<=TOTAL_SAMPLES; i++)); do
    # 显示进度
    if ((i % BATCH_SIZE == 0)) || ((i == TOTAL_SAMPLES)); then
        echo "已生成 $i / $TOTAL_SAMPLES 个样本 ($(( i * 100 / TOTAL_SAMPLES ))%)"
    fi
    
    # 生成单个样本并追加到文件
    if [[ "$CONFIG_TYPE" == "file" ]]; then
        python3 generate_test_data.py $i $CONFIG_TYPE "$CONFIG_FILE" "$CONFIG_NAME" >> "$OUTPUT_FILE"
    else
        python3 generate_test_data.py $i $CONFIG_TYPE >> "$OUTPUT_FILE"
    fi
done

# 记录结束时间
end_time=$(date +%s)
duration=$((end_time - start_time))

echo ""
echo "数据生成完成！"
echo "总样本数: $TOTAL_SAMPLES"
echo "输出文件: $OUTPUT_FILE"
echo "文件大小: $(du -h "$OUTPUT_FILE" | cut -f1)"
echo "生成耗时: ${duration}秒"
echo ""

# 显示文件统计信息
echo "文件统计信息:"
echo "行数: $(wc -l < "$OUTPUT_FILE")"
echo "字符数: $(wc -c < "$OUTPUT_FILE")"
echo "平均每行字符数: $(( $(wc -c < "$OUTPUT_FILE") / $(wc -l < "$OUTPUT_FILE") ))"
echo ""

# 显示前3个样本作为预览
echo "前3个样本预览:"
head -n 3 "$OUTPUT_FILE" | python3 -m json.tool
echo ""

echo "测试数据生成完成，可以开始性能测试了！"
