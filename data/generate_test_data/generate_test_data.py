#!/usr/bin/env python3
"""
优化的测试数据生成脚本
- 高度简洁和复用的设计
- 统一的内容生成器支持随机长度、异常情况和内容填充
- 包含PII敏感信息和异常数据生成
- 支持空值、特殊值、异常长度等边界情况
"""

import random
import json
import sys
from typing import Dict, Any, Optional, Union
from enum import Enum

class ContentType(Enum):
    """内容类型枚举"""
    NORMAL = "normal"
    PII = "pii"
    EMPTY = "empty"
    SPECIAL = "special"
    LONG = "long"
    SHORT = "short"
    MIXED = "mixed"

class TestDataGenerator:
    """统一的测试数据生成器"""

    def __init__(self, config: Dict[str, Any] = None):
        """初始化生成器"""
        self.config = config or self._get_default_config()

        # 基础词汇库（简化）
        self.words = {
            "chinese": ["数据", "分析", "算法", "模型", "机器学习", "人工智能", "系统", "平台", "用户", "安全", "优化", "创新"],
            "english": ["data", "analysis", "algorithm", "model", "system", "platform", "user", "security", "optimization", "innovation"],
            "technical": ["API", "JSON", "HTTP", "SQL", "NoSQL", "REST", "GraphQL", "Docker", "Kubernetes", "microservice"]
        }

        # PII敏感信息模板
        self.pii_data = {
            "names": ["张伟", "李娜", "王强", "刘敏", "陈静"],
            "emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "phones": ["138****1234", "159****5678", "186****9012"],
            "ids": ["110101199001011234", "320102198505052345", "******************"],
            "cards": ["6222****1234", "4367****5678", "5555****9012"]
        }

        # 内容模板（简化统一）
        self.templates = {
            "sentence": [
                "在{domain}领域中，{concept}是{adjective}的{noun}。",
                "通过{method}可以{action}{target}。",
                "{expert}认为{concept}很{adjective}。"
            ],
            "system": "你是{domain}专家，请提供专业回答。",
            "instruction": "请解释{concept}的原理和应用。",
            "response": "{concept}是{domain}中的{adjective}概念，主要用于{target}。"
        }

        # 填充词汇（简化）
        self.fill_words = {
            "domain": ["AI", "数据科学", "机器学习"],
            "concept": ["算法", "模型", "系统"],
            "adjective": ["重要", "核心", "先进"],
            "noun": ["技术", "工具", "方案"],
            "method": ["深度学习", "数据挖掘"],
            "action": ["优化", "改善", "提升"],
            "target": ["性能", "效率", "质量"],
            "expert": ["专家", "学者", "工程师"]
        }

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认字段配置"""
        return {"text": {"length": 1000}}

    def _fill_template(self, template: str) -> str:
        """填充模板占位符"""
        for key, values in self.fill_words.items():
            if f"{{{key}}}" in template:
                template = template.replace(f"{{{key}}}", random.choice(values))
        return template

    def _generate_pii_content(self) -> str:
        """生成PII敏感信息"""
        pii_types = [
            f"姓名：{random.choice(self.pii_data['names'])}",
            f"邮箱：{random.choice(self.pii_data['emails'])}",
            f"电话：{random.choice(self.pii_data['phones'])}",
            f"身份证：{random.choice(self.pii_data['ids'])}",
            f"银行卡：{random.choice(self.pii_data['cards'])}"
        ]
        return random.choice(pii_types)

    def _generate_special_content(self) -> str:
        """生成特殊内容（HTML、JSON、代码等）"""
        special_types = [
            f"<div>{random.choice(self.words['chinese'])}</div>",
            f'{{"key": "{random.choice(self.words["english"])}", "value": 123}}',
            f"SELECT * FROM {random.choice(self.words['technical'])} WHERE id > 0;",
            f"// {random.choice(self.words['english'])} function\nfunction test() {{ return true; }}",
            "NULL", "undefined", "", "   ", "\n\n\n", "特殊字符：!@#$%^&*()"
        ]
        return random.choice(special_types)

    def generate_unified_content(self, target_length: int, content_type: ContentType = ContentType.MIXED) -> str:
        """统一的内容生成器 - 支持各种异常情况和随机长度"""

        # 处理特殊情况
        if content_type == ContentType.EMPTY:
            return random.choice(["", "   ", "\n", "NULL", "null", "undefined"])

        if content_type == ContentType.SPECIAL:
            return self._generate_special_content()

        # 长度变化策略
        if content_type == ContentType.SHORT:
            target_length = random.randint(1, 50)
        elif content_type == ContentType.LONG:
            target_length = random.randint(target_length * 2, target_length * 5)
        else:
            # 随机变化目标长度 ±30%
            variance = int(target_length * 0.3)
            target_length = random.randint(max(1, target_length - variance), target_length + variance)

        content_parts = []
        current_length = 0

        while current_length < target_length:
            # 随机选择内容类型
            content_weights = {
                ContentType.NORMAL: 0.6,
                ContentType.PII: 0.15,
                ContentType.SPECIAL: 0.1,
                ContentType.MIXED: 0.15
            }

            selected_type = random.choices(
                list(content_weights.keys()),
                weights=list(content_weights.values())
            )[0]

            # 生成内容片段
            if selected_type == ContentType.PII:
                part = self._generate_pii_content()
            elif selected_type == ContentType.SPECIAL:
                part = self._generate_special_content()
            else:
                # 正常内容
                template = random.choice(self.templates["sentence"])
                part = self._fill_template(template)

                # 随机添加词汇
                if random.random() < 0.3:
                    word_type = random.choice(["chinese", "english", "technical"])
                    part += f" {random.choice(self.words[word_type])}"

            content_parts.append(part)
            current_length += len(part)

            # 随机添加分隔符
            if random.random() < 0.2 and current_length < target_length:
                separator = random.choice([" ", "\n", "。", "，", "; "])
                content_parts.append(separator)
                current_length += len(separator)

        # 合并并截断到目标长度
        result = "".join(content_parts)
        if len(result) > target_length:
            result = result[:target_length]

        return result

    def generate_content_by_type(self, field_type: str, target_length: int) -> str:
        """根据字段类型生成内容（使用统一生成器）"""
        # 根据字段类型选择内容类型
        if field_type == "system":
            template = self.templates["system"]
            base_content = self._fill_template(template)
        elif field_type == "instruction":
            template = self.templates["instruction"]
            base_content = self._fill_template(template)
        elif field_type == "response":
            template = self.templates["response"]
            base_content = self._fill_template(template)
        else:
            base_content = ""

        # 使用统一生成器补充到目标长度
        if len(base_content) < target_length:
            remaining_length = target_length - len(base_content)
            additional_content = self.generate_unified_content(remaining_length)
            base_content += " " + additional_content

        return base_content[:target_length] if len(base_content) > target_length else base_content

    def generate_multi_field_sample(self, sample_id: int = 1) -> Dict[str, Any]:
        """生成多字段样本数据（支持异常情况）"""
        sample_data = {
            "id": sample_id,
            "generated_at": f"sample_{sample_id:06d}"
        }

        # 根据配置生成各个字段
        for field_name, field_config in self.config.items():
            field_type = field_config.get("type", "text")
            target_length = field_config.get("length", 1000)

            # 随机选择内容类型（包含异常情况）
            content_type = random.choices(
                [ContentType.NORMAL, ContentType.PII, ContentType.EMPTY,
                 ContentType.SPECIAL, ContentType.LONG, ContentType.SHORT, ContentType.MIXED],
                weights=[0.5, 0.15, 0.05, 0.1, 0.1, 0.05, 0.05]
            )[0]

            # 生成字段内容
            if content_type in [ContentType.EMPTY, ContentType.SPECIAL, ContentType.SHORT, ContentType.LONG]:
                field_content = self.generate_unified_content(target_length, content_type)
            else:
                field_content = self.generate_content_by_type(field_type, target_length)
                if content_type == ContentType.PII:
                    # 在正常内容中插入PII信息
                    pii_content = self._generate_pii_content()
                    field_content = f"{field_content[:len(field_content)//2]} {pii_content} {field_content[len(field_content)//2:]}"

            sample_data[field_name] = field_content
            sample_data[f"{field_name}_length"] = len(field_content)

        return sample_data

    @staticmethod
    def load_config_from_file(config_file: str, config_name: Optional[str] = None) -> Dict[str, Any]:
        """从JSON文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                configs = json.load(f)
            if config_name and config_name in configs:
                return configs[config_name].get("fields", {})
            return next(iter(configs.values())).get("fields", {})
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

def main():
    """主函数（简化版）"""
    # 解析命令行参数
    sample_id = int(sys.argv[1]) if len(sys.argv) > 1 else 1
    config_type = sys.argv[2] if len(sys.argv) > 2 else "default"
    config_file = sys.argv[3] if len(sys.argv) > 3 else None
    config_name = sys.argv[4] if len(sys.argv) > 4 else None

    # 预定义配置
    configs = {
        "fine_tuning": {
            "system": {"type": "system", "length": 200},
            "prompt": {"type": "instruction", "length": 300},
            "response": {"type": "response", "length": 500}
        },
        "custom": {
            "system": {"type": "system", "length": 150},
            "user_input": {"type": "instruction", "length": 200},
            "assistant_output": {"type": "response", "length": 400},
            "metadata": {"type": "text", "length": 100}
        }
    }

    # 选择配置
    if config_type in configs:
        generator = TestDataGenerator(configs[config_type])
    elif config_type == "file" and config_file:
        file_config = TestDataGenerator.load_config_from_file(config_file, config_name)
        generator = TestDataGenerator(file_config) if file_config else TestDataGenerator()
    else:
        generator = TestDataGenerator()

    # 生成并输出数据
    sample_data = generator.generate_multi_field_sample(sample_id)
    print(json.dumps(sample_data, ensure_ascii=False))

if __name__ == "__main__":
    main()
