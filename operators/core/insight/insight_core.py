"""
数据洞察分析器主模块

负责协调各个子模块，生成数据洞察报告
"""

import yaml
from typing import Dict, Any
from pathlib import Path
from datetime import datetime

from .rules import BoxplotInsightRules, ProportionInsightRules, AnomalyInsightRules
from .llm_analyzer import LLMAnalyzer
from utils.logger_util import logger

class DataInsightCore:
    """数据洞察分析器"""

    def __init__(self):
        """
        初始化洞察分析器
        """
        # 统一加载模板配置文件
        self.templates = self._load_insight_templates()

        # 初始化各个子模块，传入已加载的模板配置
        self.boxplot_rules = BoxplotInsightRules(templates=self.templates)
        self.anomaly_rules = AnomalyInsightRules(templates=self.templates)
        self.proportion_rules = ProportionInsightRules(templates=self.templates)
        self.pattern_analyzer = LLMAnalyzer()

    def _load_insight_templates(self) -> Dict[str, Any]:
        """统一加载洞察规则模板配置文件"""
        # 从项目根目录的assets目录加载模板
        project_root = Path(__file__).parent.parent.parent.parent
        templates_path = project_root / 'assets' / 'insight_templates.yaml'
        try:
            with open(templates_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"加载模板配置文件失败 {e}，使用空配置")
            return {}
        
    def apply_insights(self, profile_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析数据画像报告，在原始报告中嵌入洞察结论

        Args:
            profile_report: 数据画像报告

        Returns:
            包含洞察结论的增强数据画像报告
        """
        logger.info("[insight_core]：开始为数据画像报告添加洞察结论...")

        # 深拷贝原始报告，避免修改原始数据
        import copy
        insight_report = copy.deepcopy(profile_report)

        # 1. 为箱图统计指标添加洞察
        logger.info("[insight_core]：步骤1: 为箱图统计指标添加洞察...")
        self._analyze_boxplot_stats(insight_report.get('boxplot_stats', {}))

        # 2. 为异常检测指标添加洞察
        logger.info("[insight_core]：步骤2: 为异常检测指标添加洞察...")
        self._analyze_anomaly_stats(insight_report.get('anomaly_stats', {}))

        # 3. 为占比统计指标添加洞察
        logger.info("[insight_core]：步骤3: 为占比统计指标添加洞察...")
        self._analyze_proportion_stats(insight_report.get('proportion_stats', {}))

        # 4. 为模式匹配指标添加洞察和领域洞察
        logger.info("[insight_core]：步骤4: 分析模式匹配指标和领域洞察...")
        llm_insights = self.pattern_analyzer.analyze_insights(profile_report)
        pattern_insights = llm_insights.get('pattern_insights', {})
        domain_insights = llm_insights.get('domain_insights', '')

        # 为模式匹配统计添加洞察
        pattern_stats = insight_report.get('pattern_stats', {})
        for clean_field, field_stats in pattern_stats.items():
            if isinstance(field_stats, dict):
                for pattern_field, stats in field_stats.items():
                    if isinstance(stats, dict):
                        # 从LLM分析结果中获取对应的洞察
                        # 新的数据结构：pattern_insights[clean_field][pattern_field]
                        field_insights = pattern_insights.get(clean_field, {})
                        insight_text = field_insights.get(pattern_field, '')
                        if insight_text:
                            stats['insight'] = insight_text
                        else:
                            stats['insight'] = ""

        # 5. 添加领域洞察到summary中
        if 'summary' not in insight_report:
            insight_report['summary'] = {}
        insight_report['summary']['domain_insights'] = domain_insights

        # 6. 添加洞察元数据
        insight_report['insight_metadata'] = {
            'analysis_timestamp': datetime.now().isoformat(),
            'analyzer_version': '2.0.0',
            'insights_embedded': True
        }

        print("数据画像报告洞察增强完成!")
        return insight_report
    
    def _analyze_boxplot_stats(self, boxplot_stats: Dict[str, Any]) -> None:
        """分析箱图统计指标，直接在原始数据中添加洞察"""
        # 处理数据结构：boxplot_stats[clean_field][stat_field]
        for clean_field, field_stats in boxplot_stats.items():
            if isinstance(field_stats, dict):
                for stat_field, stats in field_stats.items():
                    if isinstance(stats, dict):
                        try:
                            insight_text = self.boxplot_rules.analyze_field(stat_field, stats)
                            if insight_text:
                                stats['insight'] = insight_text
                            else:
                                stats['insight'] = ""
                        except ValueError as e:
                            logger.warning(f"箱图统计字段 '{stat_field}' 未配置: {e}")
                            insight_text = None
                            stats['insight'] = ""

    def _analyze_anomaly_stats(self, anomaly_stats: Dict[str, Any]) -> None:
        """分析异常检测指标，直接在原始数据中添加洞察"""
        # 处理数据结构：anomaly_stats[clean_field][anomaly_field]
        for clean_field, field_stats in anomaly_stats.items():
            if isinstance(field_stats, dict):
                for anomaly_field, stats in field_stats.items():
                    if isinstance(stats, dict):
                        try:
                            insight_text = self.anomaly_rules.analyze_field(anomaly_field, stats)
                            if insight_text:
                                stats['insight'] = insight_text
                            else:
                                stats['insight'] = ""
                        except ValueError as e:
                            logger.warning(f"异常检测字段 '{anomaly_field}' 未配置: {e}")
                            insight_text = None
                            stats['insight'] = ""

    def _analyze_proportion_stats(self, proportion_stats: Dict[str, Any]) -> None:
        """分析占比统计指标，直接在原始数据中添加洞察"""
        for field_name, stats in proportion_stats.items():
            if isinstance(stats, dict):
                try:
                    insight_text = self.proportion_rules.analyze_field(field_name, stats)
                    if insight_text:
                        stats['insight'] = insight_text
                    else:
                        stats['insight'] = ""
                except ValueError as e:
                    logger.warning(f"占比统计字段 '{field_name}' 未配置: {e}")
                    insight_text = None
                    stats['insight'] = ""
