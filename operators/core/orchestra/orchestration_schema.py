"""
数据清洗算子编排结果的数据结构定义

定义了编排结果的标准格式和验证逻辑
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import json


@dataclass
class OperatorParameter:
    """算子参数"""
    name: str
    value: Union[str, int, float, bool, List[Any]]


@dataclass
class OrchestrationOperator:
    """编排的清洗算子"""
    name: str  # 算子名称
    order: int  # 执行顺序，从1开始
    reason: str  # 编排理由，说明为什么选择这个算子
    parameters: List[OperatorParameter]  # 编排的参数配置
    clean_keys: List[str]  # 算子处理的字段列表


@dataclass
class CleaningOrchestration:
    """完整的清洗算子编排结果"""
    operators: List[OrchestrationOperator]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)

    def to_json(self, indent: int = 2) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CleaningOrchestration':
        """从字典创建实例"""
        # 转换operators
        operators = []
        for op_data in data['operators']:
            # 转换parameters
            parameters = []
            for param_data in op_data['parameters']:
                param = OperatorParameter(**param_data)
                parameters.append(param)

            # 创建operator
            op_data_copy = op_data.copy()
            op_data_copy['parameters'] = parameters

            # 确保clean_keys字段存在，如果不存在则设为空列表
            if 'clean_keys' not in op_data_copy:
                op_data_copy['clean_keys'] = []

            operator = OrchestrationOperator(**op_data_copy)
            operators.append(operator)

        return cls(operators=operators)
    
    def validate(self) -> List[str]:
        """验证数据结构的完整性和正确性"""
        errors = []

        # 验证基本字段
        if not self.operators:
            errors.append("编排算子列表不能为空")

        # 验证算子顺序
        orders = [op.order for op in self.operators]
        if len(set(orders)) != len(orders):
            errors.append("算子执行顺序不能重复")

        if orders and (min(orders) < 1 or max(orders) != len(orders)):
            errors.append("算子执行顺序必须从1开始且连续")

        # 验证必需字段
        for i, op in enumerate(self.operators):
            if not op.name:
                errors.append(f"算子{i+1}缺少name")
            if not op.reason:
                errors.append(f"算子{i+1}缺少编排理由")
            if op.order < 1:
                errors.append(f"算子{i+1}的执行顺序必须大于0")

        return errors
    
    def get_operators_by_order(self) -> List[OrchestrationOperator]:
        """按执行顺序排序算子"""
        return sorted(self.operators, key=lambda op: op.order)

    def get_operator_names(self) -> List[str]:
        """获取所有算子名称"""
        return [op.name for op in self.operators]


def create_orchestration_template() -> Dict[str, Any]:
    """创建编排结果的模板结构"""
    return {
        "operators": []
    }


def validate_orchestration_json(data: Dict[str, Any]) -> List[str]:
    """验证编排结果JSON的格式"""
    errors = []

    # 检查必需的顶级字段
    required_fields = ['operators']
    for field in required_fields:
        if field not in data:
            errors.append(f"缺少必需字段: {field}")

    # 检查operators字段
    if 'operators' in data:
        operators = data['operators']
        if not isinstance(operators, list):
            errors.append("operators必须是列表类型")
        else:
            for i, op in enumerate(operators):
                required_op_fields = ['name', 'order', 'reason', 'parameters']
                for field in required_op_fields:
                    if field not in op:
                        errors.append(f"算子{i+1}缺少必需字段: {field}")

                # 验证parameters字段
                if 'parameters' in op and isinstance(op['parameters'], list):
                    for j, param in enumerate(op['parameters']):
                        required_param_fields = ['name', 'value']
                        for field in required_param_fields:
                            if field not in param:
                                errors.append(f"算子{i+1}的参数{j+1}缺少必需字段: {field}")

    return errors


def create_operator_parameter(name: str, value: Any) -> OperatorParameter:
    """创建算子参数的便捷函数"""
    return OperatorParameter(name=name, value=value)


def create_orchestration_operator(name: str, order: int, reason: str,
                               parameters: Optional[List[OperatorParameter]] = None,
                               clean_keys: Optional[List[str]] = None) -> OrchestrationOperator:
    """创建编排算子的便捷函数"""
    if parameters is None:
        parameters = []
    if clean_keys is None:
        clean_keys = []
    return OrchestrationOperator(name=name, order=order, reason=reason, parameters=parameters, clean_keys=clean_keys)
