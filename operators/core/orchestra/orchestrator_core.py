"""
数据清洗算子编排器

基于数据洞察报告，使用LLM编排合适的清洗算子和配置
"""

import json
from typing import Dict, Any
from pathlib import Path

from . import CleaningOrchestration
from operators.utils.llm_client import LLMClient
from operators.utils.asset_utils import load_operators_asset
from utils.logger_util import logger


class OperatorOrchestratorCore:
    """算子编排器"""

    def __init__(self):
        """
        初始化编排器
        """
        # 加载算子配置
        self.operators_config = load_operators_asset()

        # 初始化LLM客户端，使用context中的配置
        self.llm_client = LLMClient()
        logger.info("[orchestrator_core]：LLM客户端初始化成功")
   
    def apply_orchestrate(self, insight_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于数据洞察报告编排清洗算子，并将编排结果整合到洞察报告中

        Args:
            insight_report: 数据洞察报告

        Returns:
            包含算子编排的完整洞察报告
        """
        logger.info("[orchestrator_core]：开始分析数据洞察报告并编排清洗算子...")

        # 1. 构造LLM提示词
        prompt = self._build_orchestration_prompt(insight_report)

        # 2. 调用LLM获取编排并解析响应
        orchestration = self._call_llm_and_parse_orchestration(prompt)

        # 3. 验证编排结果
        validation_errors = orchestration.validate()
        if validation_errors:
            logger.warning(f"[orchestrator_core]：编排结果验证发现问题: {validation_errors}")
            raise ValueError(f"编排结果验证失败: {validation_errors}")

        # 4. 将编排结果整合到洞察报告中
        insight_report = self._integrate_orchestration_to_report(insight_report, orchestration)

        logger.info("[orchestrator_core]：清洗算子编排完成并已整合到洞察报告中")
        return insight_report
    
    def _build_orchestration_prompt(self, insight_report: Dict[str, Any]) -> str:
        """构造LLM编排提示词"""
        
        # 获取数据集基本信息
        summary = insight_report.get('summary', {})
        total_samples = summary.get('total_samples', 0)
        analyzed_fields = summary.get('analyzed_fields', [])
        
        # 构造算子信息
        operators_info = []
        for op in self.operators_config['operators']:
            operators_info.append({
                'id': op['id'],
                'name': op.get('operator_zh_name', op.get('operator_name', op['id'])),
                'description': op.get('desc', ''),
                'parameters': op.get('parameters', [])
            })
        
        prompt = f"""
# 数据清洗算子编排任务

## 数据集概况
- 总样本数: {total_samples}
- 分析字段: {', '.join(analyzed_fields)}

## 完整数据洞察报告
{json.dumps(insight_report, ensure_ascii=False, indent=2)}
"""
        
        prompt += f"""

## 可用的清洗算子
{json.dumps(operators_info, ensure_ascii=False, indent=2)}

## 任务要求
请根据上述数据质量问题，从可用算子中选择合适的清洗算子，并按照以下JSON格式返回编排结果：

```json
{{
  "operators": [
    {{
      "name": "算子名称 operator_name",
      "order": 1,
      "reason": "编排理由，说明为什么选择这个算子",
      "clean_keys": ["字段1", "字段2"],
      "parameters": [
        {{
          "name": "参数名",
          "value": "参数值，可以是字符串、数字、布尔值或数组等，根据可用算子的描述为准"
        }}
      ]
    }}
  ]
}}
```

## 编排原则
1. 考虑算子的执行顺序，避免冲突
2. 选择效果最好的算子组合
3. 为每个算子提供合理的参数配置
4. **重要：为每个算子指定需要处理的字段（clean_keys），基于数据洞察报告中各字段的具体问题**
5. 确保编排理由充分且具体，说明针对哪些字段的哪些问题

## 字段处理说明
- clean_keys应该包含该算子需要处理的具体字段名称
- 不同算子可以处理相同或不同的字段
- 如果算子适用于所有字段，请列出所有分析字段：{analyzed_fields}
- 如果算子只适用于特定字段，请根据该字段的数据质量问题进行选择

请仅返回JSON格式的编排结果，不要包含其他文字说明。
"""
        
        return prompt

    def _call_llm_and_parse_orchestration(self, prompt: str) -> CleaningOrchestration:
        """
        调用LLM获取编排结果并解析为CleaningOrchestration对象

        合并了原来的_call_llm_for_orchestration和_parse_llm_response方法，
        利用LLMClient的call_json方法一次性完成调用和解析

        Args:
            prompt: LLM提示词

        Returns:
            CleaningOrchestration: 解析后的编排对象

        Raises:
            ValueError: 当LLM响应无效或解析失败时
            Exception: 当LLM调用失败时
        """
        try:
            print("正在调用LLM生成编排...")

            # 使用LLMClient的call_json方法一次性完成调用和JSON解析
            orchestration_data = self.llm_client.call_json(prompt)

            if not orchestration_data:
                raise ValueError("LLM响应中未找到有效的JSON格式")

            # 转换为CleaningOrchestration对象
            orchestration = CleaningOrchestration.from_dict(orchestration_data)

            print(f"成功生成并解析编排结果，共{len(orchestration.operators)}个算子")
            return orchestration

        except Exception as e:
            print(f"LLM编排生成或解析失败: {e}")
            raise

    def _save_orchestration(self, orchestration: CleaningOrchestration, output_file: str):
        """保存编排结果"""
        try:
            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(orchestration.to_json())

            print(f"编排结果已保存到: {output_file}")

        except Exception as e:
            print(f"保存编排结果失败: {e}")
            raise

    def _integrate_orchestration_to_report(self, insight_report: Dict[str, Any],
                                          orchestration: CleaningOrchestration) -> Dict[str, Any]:
        """将算子编排结果整合到洞察报告中"""
        import copy
        from datetime import datetime

        # 深拷贝原始报告，避免修改原始数据
        enhanced_report = copy.deepcopy(insight_report)

        # 添加算子编排部分
        enhanced_report['cleaning_orchestration'] = {
            'orchestration_timestamp': datetime.now().isoformat(),
            'operators': orchestration.to_dict()['operators']
        }

        logger.info(f"[orchestrator_core]：已将{len(orchestration.operators)}个编排算子整合到洞察报告中")
        return enhanced_report
