"""
- 语言判断：zh，en，other
- 编码类型判断
- 字符数
- 字母占比
- 数字占比
- 字母数字占比
- 特殊字符数
- 非标准空格数
- 控制字符数（排除常见的\n\t\r）
- 格式字符数
- 词数（英文按空格spilt统计，中文使用jieba分词）
- 停用词数
- 句子数
- 行数
- 平均词长度（英文统计，其他默认为-1）
- 平均句子长度
- 平均行长度
- 有效字符占比
- 2/3 gram重复度
- MTLD（英文和中文使用之前词数统计的分词结果）
- 敏感词匹配的列表
- PII：姓名、邮箱地址、手机号码、身份证号等匹配的片段列表
- HTML标记匹配片段列表
- 网络协议匹配片段列表
"""

import re
import jieba
import chardet
import json
import os
from typing import Dict, List, Any, Tuple
from collections import Counter
import unicodedata
from datasets import Dataset
from .helper.regex_patterns import REGEX_PATTERNS
from ...utils.asset_utils import load_words_asset, get_default_stopwords, get_default_sensitive_words

class DataProfiler:
    """数据画像分析器，用于对单个样本的各个字段分别进行全面的指标统计

    主要功能：
    - 基于clean_keys列表中的字段分别进行统计分析
    - 语言和编码检测基于所有清洗字段的拼接文本
    - 其他统计指标基于各自字段的文本内容
    - 结果按字段分别存放，使用 {field_name}_{stat_name} 的命名格式
    """
    
    def __init__(self, clean_keys):
        # 用于清洗的字段
        self.clean_keys = clean_keys
        # 使用统一的正则表达式模式管理器
        self.regex_patterns = REGEX_PATTERNS
        # 加载停用词和敏感词词典
        self._load_dictionaries()
        # 初始化jieba分词器
        jieba.initialize()

    def process(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """主要的数据画像分析方法，用于Dataset.map

        对样本中clean_keys指定的每个字段分别进行数据画像统计分析。

        Args:
            sample: 包含多个字段的样本字典

        Returns:
            包含以下结构的字典：
            - language: 基于所有清洗字段拼接文本的语言检测结果
            - encoding: 基于所有清洗字段拼接文本的编码检测结果
            - {field_name}: 每个清洗字段对应的统计指标字典
              包含该字段的所有统计指标（char_count, word_count等）
        """
        # 收集所有清洗字段的文本
        field_texts = {}
        all_texts = []

        for key in self.clean_keys:
            field_text = sample.get(key, '')
            if not isinstance(field_text, str):
                field_text = str(field_text)
            field_texts[key] = field_text
            if field_text.strip():  # 只添加非空文本
                all_texts.append(field_text)

        # 拼接所有清洗字段的文本用于语言和编码检测
        concatenated_text = ' '.join(all_texts)

        # 基于拼接文本进行语言和编码检测
        language = self.detect_language(concatenated_text)
        encoding = self.detect_encoding(concatenated_text)

        # 构建结果字典，包含全局的语言和编码信息
        result: Dict[str, Any] = {
            'language': language,
            'encoding': encoding,
        }

        # 对每个清洗字段分别进行统计分析
        for key in self.clean_keys:
            field_text = field_texts[key]

            # 如果字段为空，设置默认值
            if not field_text.strip():
                field_result = self._get_empty_field_stats()
            else:
                # 字符级别分析
                char_stats = self.analyze_characters(field_text)

                # 词级别分析
                word_stats = self.analyze_words(field_text, language)

                # 句子和行级别分析
                structure_stats = self.analyze_sentences_and_lines(field_text)

                # N-gram重复度分析
                tokens = word_stats['tokens']
                bigram_repetition = self.calculate_ngram_repetition(tokens, 2)
                trigram_repetition = self.calculate_ngram_repetition(tokens, 3)

                # MTLD计算
                mtld_score = self.calculate_mtld(tokens)

                # 模式匹配
                pattern_matches = self.find_pattern_matches(field_text)

                # 构建该字段的统计结果
                field_result = {
                    # 字符统计
                    'char_count': char_stats['char_count'],
                    'alpha_ratio': char_stats['alpha_ratio'],
                    'digit_ratio': char_stats['digit_ratio'],
                    'alnum_ratio': char_stats['alnum_ratio'],
                    'special_ratio': char_stats['special_ratio'],
                    'non_standard_spaces_ratio': char_stats['non_standard_spaces_ratio'],
                    'control_chars_ratio': char_stats['control_chars_ratio'],
                    'format_chars_ratio': char_stats['format_chars_ratio'],
                    'valid_ratio': char_stats['valid_ratio'],

                    # 词统计
                    'word_count': word_stats['word_count'],
                    'stopword_count': word_stats['stopword_count'],
                    'avg_word_length': word_stats['avg_word_length'],

                    # 结构统计
                    'sentence_count': structure_stats['sentence_count'],
                    'line_count': structure_stats['line_count'],
                    'avg_sentence_length': structure_stats['avg_sentence_length'],
                    'avg_line_length': structure_stats['avg_line_length'],

                    # 重复度和多样性
                    'bigram_repetition': bigram_repetition,
                    'trigram_repetition': trigram_repetition,
                    'mtld_score': mtld_score,

                    # 模式匹配结果
                    'pii_email': pattern_matches['pii_email'] if pattern_matches['pii_email'] else [""],
                    'pii_phone': pattern_matches['pii_phone'] if pattern_matches['pii_phone'] else [""],
                    'pii_id_card': pattern_matches['pii_id_card'] if pattern_matches['pii_id_card'] else [""],
                    'pii_name': pattern_matches['pii_name'] if pattern_matches['pii_name'] else [""],
                    'html_tags': pattern_matches['html_tags'] if pattern_matches['html_tags'] else [""],
                    'urls': pattern_matches['urls'] if pattern_matches['urls'] else [""],
                    'sensitive_words': pattern_matches['sensitive_words'] if pattern_matches['sensitive_words'] else [{'word': '', 'sensitive_type': '', 'sensitive_level': ''}],
                }

            # 将该字段的统计结果添加到总结果中，使用字段名作为key
            result[key] = field_result

        return result

    def _load_dictionaries(self):
        """加载停用词和敏感词词典"""
        # 加载停用词词典
        try:
            # 从assets目录加载停用词
            stopwords_data = load_words_asset('', 'stopwords')
            self.chinese_stopwords = stopwords_data['zh']
            self.english_stopwords = stopwords_data['en']
        except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
            print(f"警告: 无法加载停用词文件: {e}")
            # 使用默认停用词
            default_stopwords = get_default_stopwords()
            self.chinese_stopwords = default_stopwords['zh']
            self.english_stopwords = default_stopwords['en']

        # 加载敏感词词典
        try:
            # 从assets/data_profiling目录加载敏感词
            self.sensitive_words_data = load_words_asset('data_profiling', 'sensitive_words')
        except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
            print(f"警告: 无法加载敏感词文件: {e}")
            # 使用默认敏感词
            self.sensitive_words_data = get_default_sensitive_words()
    
    def detect_language(self, text: str) -> str:
        """检测文本主要语言"""
        chinese_chars = len(self.regex_patterns.chinese_pattern.findall(text))
        english_chars = len(self.regex_patterns.english_pattern.findall(text))
        total_chars = len(text.strip())

        if total_chars == 0:
            return 'other'

        chinese_ratio = chinese_chars / total_chars
        english_ratio = english_chars / total_chars

        # 优化语言检测逻辑
        # 如果中文字符占比超过20%，认为是中文为主
        if chinese_ratio > 0.2:
            return 'zh'
        # 如果英文字符占比超过50%且中文字符很少，认为是英文
        elif english_ratio > 0.5 and chinese_ratio < 0.1:
            return 'en'
        # 如果中文和英文字符都不少，但中文更多或相当，认为是中文
        elif chinese_ratio >= 0.1 and chinese_ratio >= english_ratio * 0.8:
            return 'zh'
        # 如果英文字符明显更多，认为是英文
        elif english_ratio > 0.3:
            return 'en'
        else:
            return 'other'
    
    def detect_encoding(self, text: str) -> str:
        """检测文本编码类型"""
        try:
            text_bytes = text.encode('utf-8')
            detected = chardet.detect(text_bytes)
            return detected.get('encoding', 'unknown') or 'unknown'
        except:
            return 'unknown'
    
    def analyze_characters(self, text: str) -> Dict[str, Any]:
        """分析字符级别统计"""
        # 字符总数
        char_count = len(text)
        # 字母字符总数
        alpha_count = sum(1 for c in text if c.isalpha())
        # 数字字符总数
        digit_count = sum(1 for c in text if c.isdigit())
        # 字母数字字符总数
        alnum_count = sum(1 for c in text if c.isalnum())
        # 特殊字符（非字母、数字、空格、中文的字符）
        special_count = len(self.regex_patterns.special_chars.findall(text))
        # 统计非标准空格
        non_standard_spaces_count = len(self.regex_patterns.non_standard_spaces.findall(text))
        # 统计控制字符（排除常见的文本字符如\n, \t, \r）
        common_control_chars = {9, 10, 13}  # TAB, LF, CR
        control_chars_count = sum(1 for c in text if unicodedata.category(c) == 'Cc'
                                 and ord(c) not in common_control_chars)
        # 统计格式字符
        format_chars_count = sum(1 for c in text if unicodedata.category(c) == 'Cf')

        # 计算异常字符比率（有值即异常的指标）
        special_ratio = special_count / char_count if char_count > 0 else 0.0
        non_standard_spaces_ratio = non_standard_spaces_count / char_count if char_count > 0 else 0.0
        control_chars_ratio = control_chars_count / char_count if char_count > 0 else 0.0
        format_chars_ratio = format_chars_count / char_count if char_count > 0 else 0.0

        # 计算字符占比
        alpha_ratio = alpha_count / char_count if char_count > 0 else 0.0
        digit_ratio = digit_count / char_count if char_count > 0 else 0.0
        alnum_ratio = alnum_count / char_count if char_count > 0 else 0.0

        # 计算有效字符占比（字母、数字、中文字符）
        chinese_chars = len(self.regex_patterns.chinese_pattern.findall(text))
        # 避免重复计算：alpha_count 可能包含中文字符，所以单独计算英文字母
        english_chars = sum(1 for c in text if c.isalpha() and ord(c) < 128)
        valid_chars = english_chars + digit_count + chinese_chars
        valid_ratio = valid_chars / char_count if char_count > 0 else 0.0

        return {
            'char_count': char_count,
            'alpha_ratio': alpha_ratio,
            'digit_ratio': digit_ratio,
            'alnum_ratio': alnum_ratio,
            'special_ratio': special_ratio,
            'non_standard_spaces_ratio': non_standard_spaces_ratio,
            'control_chars_ratio': control_chars_ratio,
            'format_chars_ratio': format_chars_ratio,
            'valid_ratio': valid_ratio
        }
    
    def tokenize_text(self, text: str, language: str) -> List[str]:
        """根据语言类型进行分词"""
        if language == 'zh':
            return list(jieba.cut(text))
        elif language == 'en':
            return text.split()
        else:
            return text.split()
    
    def analyze_words(self, text: str, language: str) -> Dict[str, Any]:
        """分析词级别统计"""
        tokens = self.tokenize_text(text, language)
        word_count = len(tokens)
        
        # 计算停用词数量
        if language == 'zh':
            stopwords = self.chinese_stopwords
        elif language == 'en':
            stopwords = self.english_stopwords
        else:
            stopwords = set()
        
        stopword_count = sum(1 for token in tokens if token.lower() in stopwords)
        
        # 计算平均词长度（仅英文）
        avg_word_length = -1.0
        if language == 'en' and tokens:
            avg_word_length = sum(len(token) for token in tokens) / len(tokens)
        
        return {
            'word_count': word_count,
            'stopword_count': stopword_count,
            'avg_word_length': avg_word_length,
            'tokens': tokens
        }
    
    def analyze_sentences_and_lines(self, text: str) -> Dict[str, float]:
        """分析句子和行级别统计"""
        lines = text.split('\n')
        line_count = len(lines)

        sentences = self.regex_patterns.sentence_pattern.split(text)
        sentence_count = len([s for s in sentences if s.strip()])

        # 计算平均长度
        avg_line_length = sum(len(line) for line in lines) / line_count if line_count > 0 else 0.0
        avg_sentence_length = len(text) / sentence_count if sentence_count > 0 else 0.0

        return {
            'line_count': line_count,
            'sentence_count': sentence_count,
            'avg_line_length': avg_line_length,
            'avg_sentence_length': avg_sentence_length
        }
    
    def calculate_ngram_repetition(self, tokens: List[str], n: int) -> float:
        """计算n-gram重复度"""
        if len(tokens) < n:
            return 0.0
        
        ngrams = [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]
        if not ngrams:
            return 0.0
        
        ngram_counts = Counter(ngrams)
        repeated_ngrams = sum(count - 1 for count in ngram_counts.values() if count > 1)
        
        return repeated_ngrams / len(ngrams)
    
    def calculate_mtld(self, tokens: List[str]) -> float:
        """计算MTLD (Measure of Textual Lexical Diversity)"""
        if len(tokens) < 50:
            return -1.0
        
        def ttr_segment(segment):
            if not segment:
                return 0
            return len(set(segment)) / len(segment)
        
        # 前向计算
        forward_segments = []
        current_segment = []
        
        for token in tokens:
            current_segment.append(token)
            if ttr_segment(current_segment) <= 0.72:
                forward_segments.append(len(current_segment))
                current_segment = []
        
        # 后向计算
        backward_segments = []
        current_segment = []
        
        for token in reversed(tokens):
            current_segment.append(token)
            if ttr_segment(current_segment) <= 0.72:
                backward_segments.append(len(current_segment))
                current_segment = []
        
        all_segments = forward_segments + backward_segments
        return sum(all_segments) / len(all_segments) if all_segments else -1.0
    
    def find_pattern_matches(self, text: str) -> Dict[str, List[str]]:
        """查找各种模式匹配"""
        matches = {}

        # PII匹配 - 确保返回的都是字符串列表
        for pii_type, pattern in self.regex_patterns.pii_patterns.items():
            found_matches = pattern.findall(text)
            matches[f'pii_{pii_type}'] = found_matches

        # HTML标记匹配
        html_matches = self.regex_patterns.html_pattern.findall(text)
        matches['html_tags'] = html_matches

        # 网络协议匹配
        url_matches = self.regex_patterns.url_pattern.findall(text)
        matches['urls'] = url_matches

        # 敏感词匹配
        sensitive_matches = []
        for word, word_info in self.sensitive_words_data.items():
            if word in text:
                match_info = {
                    'word': word,
                    'sensitive_type': word_info.get('sensitive_type', '未知'),
                    'sensitive_level': word_info.get('sensitive_level', '未知')
                }
                sensitive_matches.append(match_info)
        matches['sensitive_words'] = sensitive_matches

        return matches
    
    def _get_empty_field_stats(self) -> Dict[str, Any]:
        """为空字段返回默认统计值"""
        return {
            # 字符统计
            'char_count': 0,
            'alpha_ratio': 0.0,
            'digit_ratio': 0.0,
            'alnum_ratio': 0.0,
            'special_ratio': 0.0,
            'non_standard_spaces_ratio': 0.0,
            'control_chars_ratio': 0.0,
            'format_chars_ratio': 0.0,
            'valid_ratio': 0.0,

            # 词统计
            'word_count': 0,
            'stopword_count': 0,
            'avg_word_length': 0,

            # 结构统计
            'sentence_count': 0,
            'line_count': 0,
            'avg_sentence_length': 0.0,
            'avg_line_length': 0.0,

            # 重复度和多样性
            'bigram_repetition': 0.0,
            'trigram_repetition': 0.0,
            'mtld_score': -1.0,

            # 模式匹配结果
            'pii_email': [""],
            'pii_phone': [""],
            'pii_id_card': [""],
            'pii_name': [""],
            'html_tags': [""],
            'urls': [""],
            'sensitive_words': [{'word': '', 'sensitive_type': '', 'sensitive_level': ''}],
        }
