"""
异常检测指标洞察规则

针对异常检测比率指标进行洞察分析，重点关注问题样本占比和异常程度评估

异常检测指标特点：
1. 有值即异常：这些指标的期望值为0，任何大于0的值都表示存在问题
2. 双重统计：既关注涉及问题的样本占比，也关注问题样本中的异常程度
3. 影响评估：根据问题严重程度评估对训练的潜在影响

支持的异常检测指标：
- special_ratio: 特殊字符比率异常
- non_standard_spaces_ratio: 非标准空格比率异常
- control_chars_ratio: 控制字符比率异常
- format_chars_ratio: 格式字符比率异常
"""

from typing import Dict, Any, Optional
import yaml
from pathlib import Path


class AnomalyInsightRules:
    """异常检测指标洞察规则类"""

    def __init__(self, config_path: str = "insight/rules/insight_templates.yaml"):
        """
        初始化异常检测洞察规则

        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()

        # 从配置中提取各部分
        self.field_metadata = self.config.get('anomaly_field_metadata', {})
        self.thresholds = self.config.get('thresholds', {}).get('anomaly_detection', {})
        self.templates = self.config.get('templates', {}).get('anomaly', {})

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"警告：配置文件 {self.config_path} 不存在，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            print(f"警告：加载配置文件失败 {e}，使用默认配置")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'anomaly_field_metadata': {
                'special_ratio': {'name': '特殊字符异常', 'meaning': '特殊字符占比异常'},
                'non_standard_spaces_ratio': {'name': '非标准空格异常', 'meaning': '非标准空格占比异常'},
                'control_chars_ratio': {'name': '控制字符异常', 'meaning': '控制字符占比异常'},
                'format_chars_ratio': {'name': '格式字符异常', 'meaning': '格式字符占比异常'}
            },
            'thresholds': {
                'anomaly_detection': {
                    'sample_proportion': {'low': 0.05, 'moderate': 0.15, 'high': 0.30},
                    'anomaly_ratio': {'low': 0.01, 'moderate': 0.05, 'high': 0.15}
                }
            },
            'templates': {'anomaly': {}}
        }

    def analyze_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """
        分析单个异常检测字段，返回洞察文本

        Args:
            field_name: 字段名
            stats: 异常统计数据

        Returns:
            洞察文本，如果没有明显问题则返回None
        """
        if not stats:
            return None

        # 获取统计数据
        anomaly_sample_proportion = stats.get('anomaly_sample_proportion', 0)

        # 如果没有异常样本，返回None（表示无需洞察）
        if anomaly_sample_proportion == 0:
            return None

        # 检查是否有配置的指标
        if field_name in self.field_metadata:
            return self._analyze_configured_field(field_name, stats)
        else:
            # 对于未配置的字段，提供基础的异常分析
            return self._analyze_unknown_field(field_name, stats)

    def _analyze_configured_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析配置文件中定义的异常检测指标"""
        metadata = self.field_metadata[field_name]
        field_display_name = metadata['name']
        meaning = metadata['meaning']

        # 获取统计数据
        anomaly_sample_proportion = stats.get('anomaly_sample_proportion', 0)
        median_anomaly_ratio = stats.get('median_anomaly_ratio', 0)

        # 评估问题严重程度
        severity_level = self._assess_severity(anomaly_sample_proportion, median_anomaly_ratio)

        # 生成洞察文本
        insight_text = self._generate_insight_text(field_name, severity_level, stats)

        return insight_text

    def _analyze_unknown_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析未配置的异常检测字段"""
        anomaly_sample_proportion = stats.get('anomaly_sample_proportion', 0)
        median_anomaly_ratio = stats.get('median_anomaly_ratio', 0)

        severity_level = self._assess_severity(anomaly_sample_proportion, median_anomaly_ratio)

        return f"{field_name}异常：{anomaly_sample_proportion:.1%}的样本存在异常，问题程度{severity_level}"

    def _assess_severity(self, sample_proportion: float, median_ratio: float) -> str:
        """评估异常严重程度"""
        # 获取阈值配置
        sample_thresholds = self.thresholds.get('sample_proportion', {'low': 0.05, 'moderate': 0.15, 'high': 0.30})
        ratio_thresholds = self.thresholds.get('anomaly_ratio', {'low': 0.01, 'moderate': 0.05, 'high': 0.15})

        # 基于样本占比评估
        if sample_proportion <= sample_thresholds['low']:
            sample_level = "light"
        elif sample_proportion <= sample_thresholds['moderate']:
            sample_level = "moderate"
        elif sample_proportion <= sample_thresholds['high']:
            sample_level = "severe"
        else:
            sample_level = "critical"

        # 基于异常比率评估（作为辅助判断）
        if median_ratio <= ratio_thresholds['low']:
            ratio_level = "light"
        elif median_ratio <= ratio_thresholds['moderate']:
            ratio_level = "moderate"
        elif median_ratio <= ratio_thresholds['high']:
            ratio_level = "severe"
        else:
            ratio_level = "critical"

        # 综合评估：以样本占比为主，异常比率为辅
        if sample_level == "light" and ratio_level in ["light", "moderate"]:
            return "light"
        elif sample_level == "moderate" and ratio_level in ["light", "moderate"]:
            return "moderate"
        elif sample_level in ["severe"] or ratio_level in ["severe"]:
            return "severe"
        else:
            return "critical"

    def _generate_insight_text(self, field_name: str, severity_level: str, stats: Dict[str, Any]) -> str:
        """生成洞察文本"""
        # 尝试从模板获取特定的洞察文本
        template_text = self._get_template_text(field_name, severity_level, stats)
        if template_text:
            return template_text

        # 默认洞察文本生成
        anomaly_sample_proportion = stats.get('anomaly_sample_proportion', 0)
        median_anomaly_ratio = stats.get('median_anomaly_ratio', 0)
        max_anomaly_ratio = stats.get('max_anomaly_ratio', 0)

        severity_desc = {
            'light': '轻微',
            'moderate': '中等',
            'severe': '严重',
            'critical': '极严重'
        }.get(severity_level, '未知')

        return f"{field_name}异常：{anomaly_sample_proportion:.1%}的样本包含异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题程度{severity_desc}"

    def _get_template_text(self, field_name: str, severity_level: str, stats: Dict[str, Any]) -> Optional[str]:
        """从模板获取洞察文本"""
        severity_templates = self.templates.get(severity_level, {})
        template = severity_templates.get(field_name)

        if template:
            try:
                return template.format(**stats)
            except (KeyError, ValueError, ZeroDivisionError):
                return None

        return None