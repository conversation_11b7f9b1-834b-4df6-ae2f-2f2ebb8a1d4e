"""
占比统计指标洞察规则

针对分类型指标进行质量问题检测和洞察分析
"""

from typing import Dict, List, Any, Optional


class ProportionInsightRules:
    """占比统计指标洞察规则"""
    
    def __init__(self):
        # 定义各字段的期望分布和阈值
        self.field_expectations = {
            'language': {
                'expected_languages': ['zh', 'en', 'mixed'],
                'min_dominant_ratio': 0.6,  # 主要语言最小占比
                'max_languages': 5,  # 最大语言种类数
                'unknown_threshold': 0.1  # 未知语言最大占比
            },
            'encoding': {
                'expected_encodings': ['utf-8', 'gbk', 'gb2312'],
                'min_dominant_ratio': 0.8,  # 主要编码最小占比
                'max_encodings': 3,  # 最大编码种类数
                'unknown_threshold': 0.05  # 未知编码最大占比
            }
        }
    
    def analyze_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """
        分析单个占比统计字段，返回简化的洞察文本

        Args:
            field_name: 字段名
            stats: 统计数据

        Returns:
            洞察文本，如果没有问题则返回None
        """
        if not stats or stats.get('total_count', 0) == 0:
            return None

        proportions = stats.get('proportions', {})

        # 收集所有问题描述
        issues = []

        # 1. 检查分布均匀性
        distribution_issue = self._check_distribution_balance(field_name, proportions)
        if distribution_issue:
            issues.append(distribution_issue)

        # 2. 检查类别数量合理性
        category_issue = self._check_category_count(field_name, proportions)
        if category_issue:
            issues.append(category_issue)

        # 3. 检查未知/异常值比例
        unknown_issue = self._check_unknown_values(field_name, proportions)
        if unknown_issue:
            issues.append(unknown_issue)

        # 4. 字段特定检查
        field_specific_issue = self._check_field_specific_rules(field_name, proportions)
        if field_specific_issue:
            issues.append(field_specific_issue)

        # 如果没有发现问题，返回None
        if not issues:
            return None

        # 生成综合洞察文本
        return self._generate_insight_text(field_name, issues, stats)
    
    def _check_distribution_balance(self, field_name: str, proportions: Dict[str, Any]) -> Optional[str]:
        """检查分布均匀性"""
        if not proportions:
            return None

        # 获取所有类别的占比
        ratios = [item.get('proportion', 0) for item in proportions.values()]

        if not ratios:
            return None

        issues = []

        # 检查是否存在过度集中的情况
        max_ratio = max(ratios)
        expectations = self.field_expectations.get(field_name, {})
        min_dominant_ratio = expectations.get('min_dominant_ratio', 0.9)

        if max_ratio > min_dominant_ratio:
            dominant_category = None
            for category, data in proportions.items():
                if data.get('proportion', 0) == max_ratio:
                    dominant_category = category
                    break

            issues.append(f"分布过度集中于'{dominant_category}'类别({max_ratio:.2%})")

        # 检查是否存在过度分散的情况
        if len(ratios) > 1:
            # 计算基尼系数来衡量分布不均匀程度
            gini = self._calculate_gini_coefficient(ratios)

            if gini < 0.3 and len(ratios) > 3:  # 分布过于均匀且类别较多
                issues.append(f"分布过于分散({len(ratios)}个类别，基尼系数{gini:.2f})")

        if issues:
            return f"{field_name}字段" + "，".join(issues)

        return None
    
    def _check_category_count(self, field_name: str, proportions: Dict[str, Any]) -> Optional[str]:
        """检查类别数量合理性"""
        category_count = len(proportions)
        expectations = self.field_expectations.get(field_name, {})
        max_categories = expectations.get('max_categories', 10)

        issues = []

        if category_count > max_categories:
            issues.append(f"类别数量过多({category_count}个，超过建议的{max_categories}个)")

        # 检查是否存在占比极小的类别
        small_categories = []
        for category, data in proportions.items():
            ratio = data.get('proportion', 0)
            if ratio < 0.01:  # 占比小于1%
                small_categories.append(category)

        if len(small_categories) > 3:
            issues.append(f"存在{len(small_categories)}个稀有类别(占比<1%)")

        if issues:
            return f"{field_name}字段" + "，".join(issues)

        return None
    
    def _check_unknown_values(self, field_name: str, proportions: Dict[str, Any]) -> Optional[str]:
        """检查未知/异常值比例"""
        # 定义可能的未知值标识
        unknown_indicators = ['unknown', 'null', 'none', 'undefined', '', 'n/a', 'na']

        unknown_ratio = 0
        unknown_categories = []

        for category, data in proportions.items():
            if category.lower() in unknown_indicators:
                ratio = data.get('proportion', 0)
                unknown_ratio += ratio
                unknown_categories.append(category)

        expectations = self.field_expectations.get(field_name, {})
        threshold = expectations.get('unknown_threshold', 0.05)

        if unknown_ratio > threshold:
            severity = "严重" if unknown_ratio > threshold * 3 else "中等"
            return f"{field_name}字段未知值比例{severity}偏高({unknown_ratio:.2%}，超过阈值{threshold:.2%})"

        return None
    
    def _check_field_specific_rules(self, field_name: str, proportions: Dict[str, Any]) -> Optional[str]:
        """字段特定规则检查"""
        if field_name == 'language':
            return self._check_language_specific(proportions)
        elif field_name == 'encoding':
            return self._check_encoding_specific(proportions)

        return None
    
    def _check_language_specific(self, proportions: Dict[str, Any]) -> Optional[str]:
        """语言字段特定检查"""
        issues = []

        # 检查是否存在不常见的语言
        common_languages = ['zh', 'en', 'mixed', 'chinese', 'english']
        uncommon_languages = []

        for category, data in proportions.items():
            if category.lower() not in common_languages:
                ratio = data.get('proportion', 0)
                if ratio > 0.05:  # 占比超过5%的不常见语言
                    uncommon_languages.append(f"{category}({ratio:.2%})")

        if uncommon_languages:
            issues.append(f"检测到不常见语言：{', '.join(uncommon_languages)}")

        # 检查多语言混合情况
        mixed_ratio = 0
        for category, data in proportions.items():
            if 'mixed' in category.lower():
                mixed_ratio += data.get('proportion', 0)

        if mixed_ratio > 0.2:
            issues.append(f"多语言混合文本比例较高({mixed_ratio:.2%})")

        if issues:
            return "语言字段" + "，".join(issues)

        return None
    
    def _check_encoding_specific(self, proportions: Dict[str, Any]) -> Optional[str]:
        """编码字段特定检查"""
        # 检查是否存在编码问题
        problematic_encodings = ['latin-1', 'ascii', 'cp1252']
        problem_ratio = 0
        problematic_found = []

        for category, data in proportions.items():
            if category.lower() in problematic_encodings:
                ratio = data.get('proportion', 0)
                problem_ratio += ratio
                problematic_found.append(f"{category}({ratio:.2%})")

        if problem_ratio > 0.1:
            return f"编码字段检测到问题编码：{', '.join(problematic_found)}，总占比{problem_ratio:.2%}"

        return None

    def _generate_insight_text(self, field_name: str, issues: List[str], stats: Dict[str, Any]) -> str:
        """生成综合洞察文本"""
        if not issues:
            return f"{field_name}字段分布正常，无明显异常。"

        # 获取基本统计信息
        total_count = stats.get('total_count', 0)
        proportions = stats.get('proportions', {})
        category_count = len(proportions)

        # 构建洞察文本
        insight_parts = [
            f"{field_name}字段(总样本{total_count}，{category_count}个类别)存在以下问题：",
            "；".join(issues)
        ]

        # 添加建议
        recommendations = self._get_recommendations_for_issues(issues)
        if recommendations:
            insight_parts.append(f"建议：{recommendations}")

        return "".join(insight_parts) + "。"

    def _get_recommendations_for_issues(self, issues: List[str]) -> str:
        """根据问题生成建议"""
        recommendations = []

        issue_text = "".join(issues)

        if "过度集中" in issue_text:
            recommendations.append("增加数据来源多样性")

        if "过于分散" in issue_text:
            recommendations.append("进行类别合并或标准化")

        if "类别数量过多" in issue_text:
            recommendations.append("整理和标准化类别")

        if "稀有类别" in issue_text:
            recommendations.append("合并稀有类别或增加采集")

        if "未知值" in issue_text:
            recommendations.append("完善数据验证和清洗规则")

        if "不常见语言" in issue_text:
            recommendations.append("制定特殊语言处理方案")

        if "多语言混合" in issue_text:
            recommendations.append("进行语言分离处理")

        if "问题编码" in issue_text:
            recommendations.append("统一使用UTF-8编码")

        return "，".join(recommendations) if recommendations else "进一步检查数据质量"

    def _calculate_gini_coefficient(self, values: List[float]) -> float:
        """计算基尼系数"""
        if not values:
            return 0
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        cumsum = sum(sorted_values)
        
        if cumsum == 0:
            return 0
        
        gini = (2 * sum((i + 1) * val for i, val in enumerate(sorted_values))) / (n * cumsum) - (n + 1) / n
        return gini
    

