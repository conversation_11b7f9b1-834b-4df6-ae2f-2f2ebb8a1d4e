#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算子配置导出脚本
从JSON数据文件中导出所有算子的配置信息
"""

import json
import sys
import argparse
from typing import Dict, List, Any
from utils.logger_util import logger


def parse_config_temp(config_temp: str) -> List[Dict[str, Any]]:
    """
    解析config_temp字段，过滤掉不需要的参数
    
    Args:
        config_temp: JSON格式的字符串
        
    Returns:
        过滤后的参数列表
    """
    try:
        # 解析JSON字符串
        config_list = json.loads(config_temp)
        
        # 如果不是列表，返回空列表
        if not isinstance(config_list, list):
            return []
        
        # 过滤参数
        filtered_params = []
        for item in config_list:
            if not isinstance(item, dict):
                continue
                
            # 获取name字段
            name = item.get("name")
            
            # 跳过不需要的参数
            if name is None or name in ["clean_keys", "combine_process_flag"]:
                continue
                
            # 添加到结果列表
            filtered_params.append(item)
            
        return filtered_params
        
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"[export_operators_config] 任务（未指定）：解析config_temp时出错: {e}")
        return []


def extract_operators(input_file: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    从JSON文件中提取算子配置
    
    Args:
        input_file: 输入JSON文件路径
        
    Returns:
        包含算子列表的字典
    """
    try:
        # 读取JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取clean_operator字段
        clean_operators = data.get("clean_operator", [])
        
        if not isinstance(clean_operators, list):
            logger.error("[export_operators_config] 任务（未指定）：错误: clean_operator字段不是列表格式")
            return {"operators": []}
        
        # 处理每个算子
        operators = []
        for operator in clean_operators:
            if not isinstance(operator, dict):
                continue
            
            # 提取基本信息
            operator_info = {
                "id": operator.get("id"),
                "operator_name": operator.get("operator_name"),
                "operator_zh_name": operator.get("operator_zh_name"),
                "desc": operator.get("desc")
            }
            
            # 解析config_temp
            config_temp = operator.get("config_temp", "[]")
            parameters = parse_config_temp(config_temp)
            operator_info["parameters"] = parameters
            
            operators.append(operator_info)
        
        return {"operators": operators}
        
    except FileNotFoundError:
        logger.error(f"[export_operators_config] 任务（未指定）：错误: 找不到文件 {input_file}")
        return {"operators": []}
    except json.JSONDecodeError as e:
        logger.error(f"[export_operators_config] 任务（未指定）：错误: JSON解析失败 - {e}")
        return {"operators": []}
    except Exception as e:
        logger.error(f"[export_operators_config] 任务（未指定）：错误: {e}")
        return {"operators": []}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从JSON文件中导出算子配置')
    parser.add_argument('input_file', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径（可选，默认输出到控制台）')
    parser.add_argument('--pretty', action='store_true', help='格式化输出JSON')
    
    args = parser.parse_args()
    
    # 提取算子配置
    result = extract_operators(args.input_file)
    
    # 输出结果
    if args.pretty:
        output_json = json.dumps(result, ensure_ascii=False, indent=2)
    else:
        output_json = json.dumps(result, ensure_ascii=False)
    
    if args.output:
        # 输出到文件
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output_json)
            logger.info(f"[export_operators_config] 任务（未指定）：结果已保存到: {args.output}")
        except Exception as e:
            logger.error(f"[export_operators_config] 任务（未指定）：保存文件时出错: {e}")
    else:
        # 输出到控制台
        print(output_json)


if __name__ == "__main__":
    main()
