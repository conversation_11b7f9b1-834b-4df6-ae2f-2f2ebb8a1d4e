
from datasets import Dataset
import numpy as np
from typing import Dict, List, Any, Union
from collections import Counter
from datetime import datetime

from operators.core.insight import DataInsightCore


class DatasetProfilerCore:
    """数据集画像分析器，用于对数据集的指标进行统计分析"""

    def __init__(self):
        """初始化分析器
        """
        self.insight_analyzer = DataInsightCore()

        # 常规数值统计指标（箱图分析）
        self.numeric_fields = [
            'char_count', 'alpha_ratio', 'digit_ratio', 'alnum_ratio', 'valid_ratio',
            'word_count', 'stopword_count', 'avg_word_length',
            'sentence_count', 'line_count', 'avg_sentence_length', 'avg_line_length',
            'bigram_repetition', 'trigram_repetition', 'mtld_score'
        ]

        # 异常检测指标（有值即异常）
        self.anomaly_fields = [
            'special_ratio', 'non_standard_spaces_ratio', 'control_chars_ratio', 'format_chars_ratio'
        ]

        # 模式匹配字段
        self.pattern_fields = [
            'pii_email', 'pii_phone', 'pii_id_card', 'pii_name',
            'html_tags', 'urls', 'sensitive_words'
        ]

        # 全局字段（不依赖清洗字段）
        self.categorical_fields = [
            'language', 'encoding'
        ]

    def apply_profile(self, dataset: Dataset, clean_keys: List[str]) -> Dict[str, Any]:
        """分析整个数据集的画像指标"""
        if len(dataset) == 0:
            return {'error': '数据集为空'}

        print("开始分析数据集画像...")

        # 1. 计算常规数值字段的箱图统计
        boxplot_stats = {}

        # 为每个清洗字段的每个数值指标计算统计
        for clean_field in clean_keys:
            # 使用clean_field作为外部key
            boxplot_stats[clean_field] = {}

            for stat_field in self.numeric_fields:
                values = []

                for i in range(len(dataset)):
                    sample = dataset[i]
                    # 新结构：sample[clean_field][stat_field]
                    if clean_field in sample and isinstance(sample[clean_field], dict):
                        value = sample[clean_field].get(stat_field, 0)
                        if isinstance(value, (int, float)):
                            values.append(float(value))

                if values:
                    boxplot_stats[clean_field][stat_field] = self._calculate_boxplot_stats(values)
                    print(f"完成 {clean_field}.{stat_field} 的箱图统计")

        # 2. 计算异常检测字段的特殊统计
        anomaly_stats = {}

        # 为每个清洗字段的每个异常检测指标计算统计
        for clean_field in clean_keys:
            anomaly_stats[clean_field] = {}

            for anomaly_field in self.anomaly_fields:
                ratios = []

                for i in range(len(dataset)):
                    sample = dataset[i]
                    # 新结构：sample[clean_field][anomaly_field]
                    if clean_field in sample and isinstance(sample[clean_field], dict):
                        ratio = sample[clean_field].get(anomaly_field, 0.0)
                        if isinstance(ratio, (int, float)):
                            ratios.append(float(ratio))

                if ratios:
                    anomaly_stats[clean_field][anomaly_field] = self._calculate_anomaly_stats(ratios)
                    print(f"完成 {clean_field}.{anomaly_field} 的异常统计")

        # 3. 计算分类字段的占比统计
        proportion_stats = {}
        for field in self.categorical_fields:
            values = []
            for i in range(len(dataset)):
                value = dataset[i].get(field, 'unknown')
                values.append(str(value))

            if values:
                proportion_stats[field] = self._calculate_proportion_stats(values)
                print(f"完成 {field} 的占比统计")

        # 4. 计算模式匹配字段的统计
        pattern_stats = {}

        # 为每个清洗字段的每个模式匹配指标计算统计
        for clean_field in clean_keys:
            # 使用clean_field作为外部key
            pattern_stats[clean_field] = {}

            for pattern_field in self.pattern_fields:
                pattern_stats[clean_field][pattern_field] = self._calculate_pattern_stats(dataset, clean_field, pattern_field)
                print(f"完成 {clean_field}.{pattern_field} 的模式匹配统计")

        # 5. 生成总体统计摘要
        # 计算所有字段的平均文本长度
        total_char_counts = []
        total_mtld_scores = []

        for field in clean_keys:
            if field in boxplot_stats and 'char_count' in boxplot_stats[field]:
                total_char_counts.append(boxplot_stats[field]['char_count'].get('mean', 0))
            if field in boxplot_stats and 'mtld_score' in boxplot_stats[field]:
                mtld_mean = boxplot_stats[field]['mtld_score'].get('mean', 0)
                if mtld_mean > 0:  # 排除无效的MTLD分数
                    total_mtld_scores.append(mtld_mean)

        avg_text_length = sum(total_char_counts) / len(total_char_counts) if total_char_counts else 0
        content_diversity = sum(total_mtld_scores) / len(total_mtld_scores) if total_mtld_scores else 0

        summary = {
            'total_samples': len(dataset),
            'analysis_timestamp': datetime.now().isoformat(),
            'analyzed_fields': clean_keys,
            'language_distribution': proportion_stats.get('language', {}),
            'avg_text_length': round(avg_text_length, 2),
            'content_diversity': round(content_diversity, 2)
        }

        return {
            'summary': summary,
            'boxplot_stats': boxplot_stats,
            'anomaly_stats': anomaly_stats,
            'proportion_stats': proportion_stats,
            'pattern_stats': pattern_stats,
        }

    def _calculate_boxplot_stats(self, values: List[float]) -> Dict[str, Union[int, float]]:
        """计算箱图统计指标"""
        if not values:
            return {
                'count': 0, 'mean': 0.0, 'std': 0.0, 'min': 0.0,
                'q1': 0.0, 'median': 0.0, 'q3': 0.0, 'max': 0.0,
                'iqr': 0.0, 'lower_bound': 0.0, 'upper_bound': 0.0,
                'lower_outlier_count': 0, 'upper_outlier_count': 0
            }

        # 过滤掉无效值（-1表示不适用）
        valid_values = [v for v in values if v != -1.0 and not np.isnan(v)]

        if not valid_values:
            return {
                'count': 0, 'mean': 0.0, 'std': 0.0, 'min': 0.0,
                'q1': 0.0, 'median': 0.0, 'q3': 0.0, 'max': 0.0,
                'iqr': 0.0, 'lower_bound': 0.0, 'upper_bound': 0.0,
                'lower_outlier_count': 0, 'upper_outlier_count': 0
            }

        values_array = np.array(valid_values)

        # 基础统计
        count = len(valid_values)
        mean = float(np.mean(values_array))
        std = float(np.std(values_array))
        min_val = float(np.min(values_array))
        max_val = float(np.max(values_array))

        # 分位数
        q1 = float(np.percentile(values_array, 25))
        median = float(np.percentile(values_array, 50))
        q3 = float(np.percentile(values_array, 75))
        iqr = q3 - q1

        # 异常值检测（1.5倍IQR规则）
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        # 分别计算上限和下限异常值
        lower_outliers = values_array[values_array < lower_bound]
        upper_outliers = values_array[values_array > upper_bound]
        lower_outlier_count = len(lower_outliers)
        upper_outlier_count = len(upper_outliers)

        return {
            'count': count,
            'mean': round(mean, 4),
            'std': round(std, 4),
            'min': round(min_val, 4),
            'q1': round(q1, 4),
            'median': round(median, 4),
            'q3': round(q3, 4),
            'max': round(max_val, 4),
            'iqr': round(iqr, 4),
            'lower_bound': round(lower_bound, 4),
            'upper_bound': round(upper_bound, 4),
            'lower_outlier_count': lower_outlier_count,
            'upper_outlier_count': upper_outlier_count
        }

    def _calculate_proportion_stats(self, values: List[str]) -> Dict[str, Any]:
        """计算占比统计指标"""
        if not values:
            return {'total_count': 0, 'proportions': {}, 'most_common': None}

        counter = Counter(values)
        total_count = len(values)

        # 计算占比
        proportions = {}
        for value, count in counter.items():
            proportions[value] = {
                'count': count,
                'proportion': round(count / total_count, 4)
            }

        # 最常见的值
        most_common = counter.most_common(1)[0] if counter else None

        return {
            'total_count': total_count,
            'proportions': proportions,
            'most_common': most_common
        }

    def _calculate_anomaly_stats(self, ratios: List[float]) -> Dict[str, Any]:
        """计算异常检测指标的统计信息

        对于异常检测比率指标（special_ratio、non_standard_spaces_ratio等），
        这些指标有值就代表异常，统计方法：
        1. 包含该类问题的样本比例
        2. 包含该类问题样本的异常比率统计（中位数、平均值、最大值等）

        Args:
            ratios: 异常比率列表（0.0-1.0之间的值）

        Returns:
            包含异常统计信息的字典
        """
        # 定义默认返回值，避免重复代码
        default_stats = {
            'anomaly_sample_proportion': 0.0,
            'median_anomaly_ratio': 0.0,
            'mean_anomaly_ratio': 0.0,
            'max_anomaly_ratio': 0.0,
            'min_anomaly_ratio': 0.0
        }

        # 早期返回：空数据或无效数据
        if not ratios:
            return default_stats

        # 过滤掉无效值（-1表示不适用）
        valid_ratios = [r for r in ratios if r != -1.0 and not np.isnan(r)]
        if not valid_ratios:
            return default_stats

        # 找出有异常的样本（比率 > 0）
        anomaly_ratios = [r for r in valid_ratios if r > 0]

        # 计算包含异常的样本比例
        anomaly_sample_proportion = len(anomaly_ratios) / len(valid_ratios)

        # 如果没有异常样本，返回默认值（但包含正确的样本比例）
        if not anomaly_ratios:
            return {**default_stats, 'anomaly_sample_proportion': round(anomaly_sample_proportion, 4)}

        # 计算统计信息
        stats = {
            'anomaly_sample_proportion': round(anomaly_sample_proportion, 4),
            'median_anomaly_ratio': round(float(np.median(anomaly_ratios)), 4),
            'mean_anomaly_ratio': round(float(np.mean(anomaly_ratios)), 4),
            'max_anomaly_ratio': round(float(np.max(anomaly_ratios)), 4),
            'min_anomaly_ratio': round(float(np.min(anomaly_ratios)), 4)
        }

        return stats

    def _calculate_pattern_stats(self, dataset: Dataset, clean_field: str, pattern_field: str) -> Dict[str, Any]:
        """计算特定字段的模式匹配统计指标"""
        total_samples = len(dataset)

        if pattern_field == 'sensitive_words':
            # 敏感词特殊处理
            has_sensitive = 0
            sensitive_types = Counter()
            samples_with_type = {}
            sensitive_levels = Counter()
            samples_with_level = {}
            all_sensitive_words = []

            for i in range(total_samples):
                sample = dataset[i]
                # 新结构：sample[clean_field][pattern_field]
                if clean_field in sample and isinstance(sample[clean_field], dict):
                    sensitive_list = sample[clean_field].get(pattern_field, [])

                    if sensitive_list:
                        has_sensitive += 1
                        sample_types = set()
                        sample_levels = set()

                        for item in sensitive_list:
                            if isinstance(item, dict):
                                word = item.get('word', '')
                                sensitive_type = item.get('sensitive_type', '未知')
                                sensitive_level = item.get('sensitive_level', '未知')

                                sensitive_types[sensitive_type] += 1
                                sensitive_levels[sensitive_level] += 1
                                all_sensitive_words.append(word)

                                sample_types.add(sensitive_type)
                                sample_levels.add(sensitive_level)

                        for s_type in sample_types:
                            samples_with_type[s_type] = samples_with_type.get(s_type, 0) + 1

                        for s_level in sample_levels:
                            samples_with_level[s_level] = samples_with_level.get(s_level, 0) + 1

            # 构建统计结果
            type_stats = {}
            for s_type, count in sensitive_types.items():
                samples_count = samples_with_type.get(s_type, 0)
                type_stats[s_type] = {
                    'word_count': count,
                    'sample_count': samples_count,
                    'sample_proportion': round(samples_count / total_samples, 4) if total_samples > 0 else 0
                }

            level_stats = {}
            for s_level, count in sensitive_levels.items():
                samples_count = samples_with_level.get(s_level, 0)
                level_stats[s_level] = {
                    'word_count': count,
                    'sample_count': samples_count,
                    'sample_proportion': round(samples_count / total_samples, 4) if total_samples > 0 else 0
                }

            return {
                'total_samples': total_samples,
                'samples_with_pattern': has_sensitive,
                'proportion': round(has_sensitive / total_samples, 4) if total_samples > 0 else 0,
                'by_type': type_stats,
                'by_level': level_stats,
                'unique_words': len(set(all_sensitive_words)),
                'most_common_words': Counter(all_sensitive_words).most_common(3)
            }
        else:
            # 其他模式匹配字段
            has_pattern = 0
            all_matches = []

            for i in range(total_samples):
                sample = dataset[i]
                # 新结构：sample[clean_field][pattern_field]
                if clean_field in sample and isinstance(sample[clean_field], dict):
                    pattern_list = sample[clean_field].get(pattern_field, [])
                    # 检查是否有非空匹配
                    if pattern_list and any(match.strip() for match in pattern_list if match):
                        has_pattern += 1
                        all_matches.extend([match for match in pattern_list if match.strip()])

            return {
                'total_samples': total_samples,
                'samples_with_pattern': has_pattern,
                'proportion': round(has_pattern / total_samples, 4) if total_samples > 0 else 0,
                'most_common_matches': Counter(all_matches).most_common(3) if all_matches else []
            }