"""
正则表达式模式统一管理模块
包含数据画像分析中使用的所有正则表达式模式，提供预编译的正则对象
"""

import re
from typing import Dict, Pattern
from utils.logger_util import logger


class RegexPatterns:
    """正则表达式模式管理类，提供预编译的正则表达式对象"""
    
    def __init__(self):
        """初始化并编译所有正则表达式模式"""
        self._compile_all_patterns()
    
    def _compile_all_patterns(self):
        """编译所有正则表达式模式"""
        # PII（个人身份信息）相关模式
        self._compile_pii_patterns()
        
        # HTML和网络协议模式
        self._compile_web_patterns()
        
        # 字符和空格模式
        self._compile_character_patterns()
        
        # 语言检测模式
        self._compile_language_patterns()
        
        # 文本结构模式
        self._compile_structure_patterns()
    
    def _compile_pii_patterns(self):
        """编译PII相关的正则表达式模式"""
        self.pii_patterns: Dict[str, Pattern] = {
            # 邮箱地址模式 - 支持常见的邮箱格式，去掉单词边界以适应中文环境
            'email': re.compile(
                r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}',
                re.IGNORECASE
            ),

            # 中国大陆手机号码模式 - 1开头，第二位3-9，共11位
            'phone': re.compile(r'1[3-9]\d{9}'),

            # 中国大陆身份证号码模式 - 18位，最后一位可能是X
            'id_card': re.compile(r'\d{17}[\dXx]'),

            # 中文姓名模式 - 2-4个中文字符，后跟常见称谓
            'name': re.compile(
                r'[\u4e00-\u9fff]{2,4}(?=先生|女士|同志|老师|医生|教授|博士|硕士|学士|同学)'
            ),

            # 银行卡号模式 - 13-19位数字
            'bank_card': re.compile(r'\d{13,19}'),

            # 护照号码模式 - 字母+数字组合
            'passport': re.compile(r'[A-Z]\d{8}|[A-Z]{2}\d{7}'),

            # 社会统一信用代码 - 18位字母数字组合
            'credit_code': re.compile(r'[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}'),
        }
    
    def _compile_web_patterns(self):
        """编译网络相关的正则表达式模式"""
        # HTML标签模式 - 匹配所有HTML标签
        self.html_pattern = re.compile(r'<[^>]+>', re.IGNORECASE)
        
        # URL模式 - 匹配HTTP/HTTPS/FTP/WWW等网址
        self.url_pattern = re.compile(
            r'(?:https?://|ftp://|www\.)[^\s<>"\']+|'
            r'[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?'
            r'(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*'
            r'\.[a-zA-Z]{2,}(?:/[^\s<>"\']*)?',
            re.IGNORECASE
        )
        
        # IP地址模式 - IPv4地址
        self.ip_pattern = re.compile(
            r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}'
            r'(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
        )
        
        # 域名模式
        self.domain_pattern = re.compile(
            r'\b[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?'
            r'(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*'
            r'\.[a-zA-Z]{2,}\b',
            re.IGNORECASE
        )
    
    def _compile_character_patterns(self):
        """编译字符相关的正则表达式模式"""
        # 特殊字符模式 - 排除常见的文本字符，只保留真正的特殊字符
        # 排除：英文字母、数字、常见标点符号、空格、中文字符、日韩文字符、常见符号
        self.special_chars = re.compile(
            r'[^'
            r'a-zA-Z0-9'                    # 英文字母和数字
            r'\u4e00-\u9fff'                # 中文字符(CJK统一汉字)
            r'\u3040-\u309f'                # 日文平假名
            r'\u30a0-\u30ff'                # 日文片假名
            r'\uac00-\ud7af'                # 韩文
            r'\s'                           # 所有空白字符
            r'.,;:!?\'"()\[\]{}'            # 常见英文标点符号(转义方括号)
            r'，。；：！？""''（）【】《》'    # 常见中文标点符号
            r'、·…—\-'                      # 其他常见符号(转义减号)
            r'@#$%&*+=/<>|\\^`~_'          # 常见特殊符号但在文本中正常出现
            r']',
            re.UNICODE
        )

        # 非标准空格模式 - 各种Unicode空格字符
        self.non_standard_spaces = re.compile(
            r'[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000]'
        )
        
        # 数字模式
        self.digits = re.compile(r'\d+')
        
        # 字母模式
        self.letters = re.compile(r'[a-zA-Z]+')
        
        # 标点符号模式
        self.punctuation = re.compile(r'[^\w\s]', re.UNICODE)
        
        # 表情符号模式 - Unicode表情符号范围
        self.emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情符号
            r'[\U0001F300-\U0001F5FF]|'  # 符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图符号
            r'[\U0001F1E0-\U0001F1FF]|'  # 区域指示符号
            r'[\U00002702-\U000027B0]|'  # 杂项符号
            r'[\U000024C2-\U0001F251]'   # 封闭字母数字
        )
    
    def _compile_language_patterns(self):
        """编译语言检测相关的正则表达式模式"""
        # 中文字符模式 - CJK统一汉字
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        
        # 英文字符模式 - 基本拉丁字母
        self.english_pattern = re.compile(r'[a-zA-Z]')
        
        # 日文平假名模式
        self.hiragana_pattern = re.compile(r'[\u3040-\u309f]')
        
        # 日文片假名模式
        self.katakana_pattern = re.compile(r'[\u30a0-\u30ff]')
        
        # 韩文模式
        self.korean_pattern = re.compile(r'[\uac00-\ud7af]')
        
        # 阿拉伯文模式
        self.arabic_pattern = re.compile(r'[\u0600-\u06ff]')
        
        # 俄文模式
        self.cyrillic_pattern = re.compile(r'[\u0400-\u04ff]')
    
    def _compile_structure_patterns(self):
        """编译文本结构相关的正则表达式模式"""
        # 句子分割模式 - 中英文句号、问号、感叹号
        self.sentence_pattern = re.compile(r'[.!?。！？]+')
        
        # 段落分割模式 - 多个换行符
        self.paragraph_pattern = re.compile(r'\n\s*\n')
        
        # 行首空格模式
        self.line_indent = re.compile(r'^\s+', re.MULTILINE)
        
        # 连续空格模式
        self.multiple_spaces = re.compile(r'\s{2,}')
        
        # 换行符模式
        self.newlines = re.compile(r'\r?\n')
        
        # 制表符模式
        self.tabs = re.compile(r'\t+')
    
    def get_pii_patterns(self) -> Dict[str, Pattern]:
        """获取PII相关的正则表达式模式字典"""
        return self.pii_patterns.copy()
    
    def get_all_patterns(self) -> Dict[str, Pattern]:
        """获取所有正则表达式模式的字典"""
        patterns = {}
        
        # 添加PII模式
        for key, pattern in self.pii_patterns.items():
            patterns[f'pii_{key}'] = pattern
        
        # 添加其他单个模式
        single_patterns = {
            'html': self.html_pattern,
            'url': self.url_pattern,
            'ip': self.ip_pattern,
            'domain': self.domain_pattern,
            'special_chars': self.special_chars,
            'non_standard_spaces': self.non_standard_spaces,
            'digits': self.digits,
            'letters': self.letters,
            'punctuation': self.punctuation,
            'emoji': self.emoji_pattern,
            'chinese': self.chinese_pattern,
            'english': self.english_pattern,
            'hiragana': self.hiragana_pattern,
            'katakana': self.katakana_pattern,
            'korean': self.korean_pattern,
            'arabic': self.arabic_pattern,
            'cyrillic': self.cyrillic_pattern,
            'sentence': self.sentence_pattern,
            'paragraph': self.paragraph_pattern,
            'line_indent': self.line_indent,
            'multiple_spaces': self.multiple_spaces,
            'newlines': self.newlines,
            'tabs': self.tabs,
        }
        
        patterns.update(single_patterns)
        return patterns


# 创建全局实例，提供预编译的正则表达式对象
REGEX_PATTERNS = RegexPatterns()

# 为了向后兼容，提供直接访问的变量
PII_PATTERNS = REGEX_PATTERNS.get_pii_patterns()
HTML_PATTERN = REGEX_PATTERNS.html_pattern
URL_PATTERN = REGEX_PATTERNS.url_pattern
SPECIAL_CHARS_PATTERN = REGEX_PATTERNS.special_chars
NON_STANDARD_SPACES_PATTERN = REGEX_PATTERNS.non_standard_spaces
CHINESE_PATTERN = REGEX_PATTERNS.chinese_pattern
ENGLISH_PATTERN = REGEX_PATTERNS.english_pattern
SENTENCE_PATTERN = REGEX_PATTERNS.sentence_pattern


def test_patterns():
    """测试正则表达式模式的功能"""
    test_text = """
    这是一个测试文本，包含多种信息：
    联系方式：张三先生，邮箱****************，电话13812345678
    网址：https://www.example.com
    身份证：123456789012345678
    <html><body>HTML标记</body></html>
    特殊字符：!@#$%^&*()
    """
    
    patterns = REGEX_PATTERNS

    logger.info("[regex_patterns] 任务（测试）：=== 正则表达式模式测试 ===")

    # 测试PII模式
    logger.info("[regex_patterns] 任务（测试）：--- PII检测 ---")
    for pii_type, pattern in patterns.pii_patterns.items():
        matches = pattern.findall(test_text)
        if matches:
            logger.info(f"[regex_patterns] 任务（测试）：{pii_type}: {matches}")

    # 测试其他模式
    logger.info("[regex_patterns] 任务（测试）：--- 其他模式检测 ---")
    html_matches = patterns.html_pattern.findall(test_text)
    if html_matches:
        logger.info(f"[regex_patterns] 任务（测试）：HTML标签: {html_matches}")

    url_matches = patterns.url_pattern.findall(test_text)
    if url_matches:
        logger.info(f"[regex_patterns] 任务（测试）：URL: {url_matches}")

    chinese_count = len(patterns.chinese_pattern.findall(test_text))
    english_count = len(patterns.english_pattern.findall(test_text))
    logger.info(f"[regex_patterns] 任务（测试）：中文字符数: {chinese_count}")
    logger.info(f"[regex_patterns] 任务（测试）：英文字符数: {english_count}")


if __name__ == "__main__":
    test_patterns()
