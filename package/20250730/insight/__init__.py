"""
数据洞察模块

该模块用于分析数据画像报告，生成简洁的数据质量洞察。

主要功能：
1. 分析boxplot_stats指标，生成数值分布洞察文本
2. 分析proportion_stats指标，生成分类分布洞察文本
3. 分析pattern_stats指标，生成模式匹配洞察文本
4. 识别数据集领域类型

使用示例：
    from insight import DataInsightAnalyzer

    analyzer = DataInsightAnalyzer()
    insights = analyzer.analyze_profile_report(profile_report)
    # 返回格式：{'boxplot_insights': {}, 'proportion_insights': {}, 'pattern_insights': {}, 'domain_insights': ''}
"""

from .insight_core import DataInsightCore
