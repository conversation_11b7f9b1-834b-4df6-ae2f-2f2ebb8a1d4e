"""
箱图统计指标洞察规则

针对数值型指标进行洞察分析，重点关注异常值和整体分布特征，提供事实分析和启发式原因分析

使用配置文件和模板引擎，实现高度灵活和专业的洞察规则系统

异常值：

1. 如果有正常范围，判断最小值最大值是否有处于异常的情况，并提供明确的洞察意见。
2. 如果没有正常范围，基于IQR的内限划分异常值，仅描述有多少异常值处于该范围，中位数是多少作为参考。

特征分布：

1. 中位数在正常范围内但偏低或偏高
2. IQR
2. CV
3. 偏斜度

"""

from typing import Dict, Any, Optional


class BoxplotInsightRules:
    """箱图统计指标洞察规则"""

    def __init__(self, templates: Dict[str, Any]):
        """
        初始化箱图洞察规则

        Args:
            templates: 模板配置字典
        """
        self.config = templates
        self.field_metadata = self.config.get('boxplot_field_metadata', {})
        self.thresholds = self.config.get('thresholds', {})
        self.templates = self.config.get('templates', {})

    def analyze_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """
        分析单个箱图统计字段，返回洞察文本

        重点关注异常值优先和整体分布特征：
        1. 异常值优先：基于超过上限或下限的异常数量和比例，结合具体指标的含义
        2. 整体分布特征：使用中位数、IQR、变异系数、偏斜度进行洞察

        Args:
            field_name: 字段名
            stats: 统计数据

        Returns:
            洞察文本，如果没有明显特征则返回None

        Raises:
            ValueError: 当字段未在配置中定义时抛出异常
        """
        if not stats or stats.get('count', 0) == 0:
            return None

        # 检查是否有配置的指标
        if field_name in self.field_metadata:
            return self._analyze_configured_field(field_name, stats)
        else:
            # 对于未配置的字段，抛出错误以确保所有指标都有明确配置
            raise ValueError(f"箱图统计字段 '{field_name}' 未在配置文件中定义。请在 boxplot_field_metadata 中添加该字段的配置信息。")

    def _analyze_configured_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析配置文件中定义的指标"""
        metadata = self.field_metadata[field_name]
        field_display_name = metadata['name']
        meaning = metadata['meaning']

        # 获取该字段配置的分析维度
        analysis_dimensions = metadata.get('analysis_dimensions', ['outliers', 'median', 'iqr', 'cv', 'skewness'])

        insights = []

        # 1. 异常值分析（如果配置了outliers维度）
        if 'outliers' in analysis_dimensions:
            outlier_insight = self._analyze_outliers(field_name, stats, metadata)
            if outlier_insight:
                insights.append(outlier_insight)

        # 2. 分布特征分析（根据配置的维度）
        distribution_insight = self._analyze_distribution_features(field_name, stats, metadata)
        if distribution_insight:
            insights.append(distribution_insight)

        if not insights:
            return None

        # 构建最终洞察文本
        insight_text = f"{field_display_name}({meaning})："
        insight_text += "；".join(insights)

        return insight_text

    def _analyze_outliers(self, field_name: str, stats: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[str]:
        """
        统一的异常值分析方法

        根据是否有正常范围参考，选择不同的分析策略：
        1. 有正常范围：判断最小值最大值是否超出正常范围，提供明确的洞察
        2. 无正常范围：基于IQR描述异常值数量和比例，仅描述有多少异常值，中位数作为参考
        """
        count = stats.get('count', 0)
        if count == 0:
            return None

        normal_range = metadata.get('normal_range')

        if normal_range and len(normal_range) == 2:
            # 有正常范围：范围异常值分析
            return self._analyze_normal_range_outliers(field_name, stats, metadata)
        else:
            # 无正常范围：统计异常值分析
            return self._analyze_statistical_outliers(field_name, stats)

    def _analyze_normal_range_outliers(self, field_name: str, stats: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[str]:
        """基于正常范围分析真正的异常值"""
        normal_range = metadata.get('normal_range')
        if not normal_range or len(normal_range) != 2:
            return None

        min_val = stats.get('min', 0)
        max_val = stats.get('max', 0)
        min_normal, max_normal = normal_range
        insights = []

        # 检查最小值是否低于正常范围（只有当下限不为None时才检查）
        if min_normal is not None and min_val < min_normal:
            template = self._get_template('outliers', 'normal_range_based', 'below_range', field_name)
            if template:
                insight = template.format(min_normal=min_normal)
                insights.append(insight)

        # 检查最大值是否超过正常范围（只有当上限不为None时才检查）
        if max_normal is not None and max_val > max_normal:
            template = self._get_template('outliers', 'normal_range_based', 'above_range', field_name)
            if template:
                insight = template.format(max_normal=max_normal)
                insights.append(insight)

        return "；".join(insights) if insights else None

    def _analyze_statistical_outliers(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """基于IQR的统计异常值分析，客观描述异常值情况"""
        count = stats.get('count', 0)
        lower_outlier_count = stats.get('lower_outlier_count', 0)
        upper_outlier_count = stats.get('upper_outlier_count', 0)
        lower_bound = stats.get('lower_bound', 0)
        upper_bound = stats.get('upper_bound', 0)

        if count == 0:
            return None

        outlier_count = lower_outlier_count + upper_outlier_count
        outlier_ratio = outlier_count / count

        # 只关注显著的异常值情况
        if outlier_ratio < self.thresholds['outlier_ratio']['moderate']:
            return None

        insights = []

        if lower_outlier_count > 0:
            template = self._get_template('outliers', 'iqr_based', 'lower', field_name)

            if template:
                insight = template.format(
                    count=lower_outlier_count,
                    lower_bound=lower_bound
                )
                insights.append(insight)

        if upper_outlier_count > 0:
            template = self._get_template('outliers', 'iqr_based', 'upper', field_name)

            if template:
                insight = template.format(
                    count=upper_outlier_count,
                    upper_bound=upper_bound
                )
                insights.append(insight)

        return "；".join(insights) if insights else None

    def _analyze_distribution_features(self, field_name: str, stats: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[str]:
        """
        根据字段配置的analysis_dimensions选择性分析分布特征：
        1. median: 中位数偏向分析（仅对有normal_range的指标）
        2. iqr: IQR分析
        3. cv: 变异系数分析
        4. skewness: 偏斜度分析
        """
        insights = []

        # 获取该字段配置的分析维度，如果没有配置则使用默认的全部分析
        analysis_dimensions = metadata.get('analysis_dimensions', ['median', 'iqr', 'cv', 'skewness'])

        # 1. 中位数分析（仅对有normal_range的指标且配置了median维度）
        if 'median' in analysis_dimensions:
            median_insight = self._analyze_median_bias(field_name, stats, metadata)
            if median_insight:
                insights.append(median_insight)

        # 2. IQR分析
        if 'iqr' in analysis_dimensions:
            iqr_insight = self._analyze_iqr(field_name, stats)
            if iqr_insight:
                insights.append(iqr_insight)

        # 3. 变异系数分析
        if 'cv' in analysis_dimensions:
            cv_insight = self._analyze_cv(field_name, stats)
            if cv_insight:
                insights.append(cv_insight)

        # 4. 偏斜度分析
        if 'skewness' in analysis_dimensions:
            skewness_insight = self._analyze_skewness(field_name, stats)
            if skewness_insight:
                insights.append(skewness_insight)

        return "；".join(insights) if insights else None

    def _analyze_median_bias(self, field_name: str, stats: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[str]:
        """分析中位数偏向（仅对有normal_range的指标）"""
        normal_range = metadata.get('normal_range')
        if not normal_range or len(normal_range) != 2:
            return None

        median = stats.get('median', 0)
        min_normal, max_normal = normal_range

        # 如果两个边界都是None，则无法进行分析
        if min_normal is None and max_normal is None:
            return None

        # 检查是否超出正常范围
        if min_normal is not None and median < min_normal:
            template = self._get_template('distribution', 'median', 'below_range', field_name)
            if template:
                return template
        elif max_normal is not None and median > max_normal:
            template = self._get_template('distribution', 'median', 'above_range', field_name)
            if template:
                return template

        # 只有当两个边界都不为None时才进行偏向分析
        if min_normal is not None and max_normal is not None:
            range_span = max_normal - min_normal
            # 计算偏向阈值
            low_threshold = min_normal + range_span * self.thresholds['median_bias']['low_threshold']
            high_threshold = min_normal + range_span * self.thresholds['median_bias']['high_threshold']

            if median < low_threshold:
                template = self._get_template('distribution', 'median', 'low', field_name)
                if template:
                    return template
            elif median > high_threshold:
                template = self._get_template('distribution', 'median', 'high', field_name)
                if template:
                    return template

        return None

    def _analyze_iqr(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析IQR（四分位距）"""
        q1 = stats.get('q1', 0)
        q3 = stats.get('q3', 0)
        median = stats.get('median', 0)

        if q1 == 0 and q3 == 0:
            return None

        iqr = q3 - q1
        if median != 0:
            iqr_ratio = iqr / median
        else:
            iqr_ratio = iqr

        if iqr_ratio < self.thresholds['iqr_ratio']['narrow']:
            template = self._get_template('distribution', 'iqr', 'narrow', field_name)
            if template:
                return template.format(iqr=iqr)
        elif iqr_ratio > self.thresholds['iqr_ratio']['wide']:
            template = self._get_template('distribution', 'iqr', 'wide', field_name)
            if template:
                return template.format(iqr=iqr)

        return None

    def _analyze_cv(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析变异系数"""
        mean = stats.get('mean', 0)
        std = stats.get('std', 0)

        if mean == 0:
            return None

        cv = std / mean

        if cv < self.thresholds['cv']['low']:
            template = self._get_template('distribution', 'cv', 'low', field_name)
        elif cv < self.thresholds['cv']['moderate']:
            return None  # 适中范围不报告
        elif cv < self.thresholds['cv']['high']:
            template = self._get_template('distribution', 'cv', 'moderate', field_name)
        elif cv < self.thresholds['cv']['very_high']:
            template = self._get_template('distribution', 'cv', 'high', field_name)
        else:
            template = self._get_template('distribution', 'cv', 'very_high', field_name)

        if template:
            return template.format(cv=cv)

        return None

    def _analyze_skewness(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """分析偏斜度"""
        # 计算偏斜度（简化计算）
        mean = stats.get('mean', 0)
        median = stats.get('median', 0)
        std = stats.get('std', 0)

        if std == 0:
            return None

        # 使用Pearson偏斜度系数的简化版本
        skewness = 3 * (mean - median) / std

        abs_skewness = abs(skewness)
        direction = 'positive' if skewness > 0 else 'negative'

        if abs_skewness < self.thresholds['skewness']['moderate']:
            return None  # 偏斜度不显著
        elif abs_skewness < self.thresholds['skewness']['high']:
            template = self._get_skewness_template('moderate', direction, field_name)
        elif abs_skewness < self.thresholds['skewness']['severe']:
            template = self._get_skewness_template('high', direction, field_name)
        else:
            template = self._get_skewness_template('severe', direction, field_name)

        if template:
            return template.format(skewness=skewness)

        return None

    def _get_skewness_template(self, severity: str, direction: str, field_name: Optional[str] = None) -> Optional[str]:
        """
        获取偏度分析的专门模板

        Args:
            severity: 严重程度 (moderate, high, severe)
            direction: 偏向方向 (positive, negative)
            field_name: 字段名（可选，用于获取特定字段的模板）
        """
        try:
            templates = self.templates['distribution']['skewness'][severity][direction]

            # 如果field_name提供且存在特定模板，优先使用
            if field_name and field_name in templates:
                return templates[field_name]

            # 否则使用默认模板
            if 'default' in templates:
                return templates['default']

        except (KeyError, TypeError):
            pass

        return None

    def _get_template(self, category: str, subcategory: str, condition: str, field_name: Optional[str] = None) -> Optional[str]:
        """
        统一的模板获取方法

        Args:
            category: 模板类别 (outliers, distribution)
            subcategory: 子类别 (normal_range_based, iqr_based, median, iqr, cv, skewness)
            condition: 条件 (lower, upper, below_range, above_range, low, high, etc.)
            field_name: 字段名（可选，用于获取特定字段的模板）
        """
        try:
            templates = self.templates[category][subcategory]

            # 如果condition是字符串，直接获取
            if isinstance(condition, str):
                condition_templates = templates.get(condition, {})
            else:
                return None

            # 如果field_name提供且存在特定模板，优先使用
            if field_name and field_name in condition_templates:
                return condition_templates[field_name]

            # 否则使用默认模板
            if 'default' in condition_templates:
                return condition_templates['default']

            # 如果condition_templates是字符串，直接返回
            if isinstance(condition_templates, str):
                return condition_templates

        except (KeyError, TypeError):
            pass

        return None


