"""
占比统计指标洞察规则

基于模板配置的占比统计指标洞察分析
"""

from typing import Dict, Any, Optional


class ProportionInsightRules:
    """占比统计指标洞察规则 - 基于模板配置"""

    def __init__(self, templates: Dict[str, Any]):
        """
        初始化规则

        Args:
            templates: 模板配置字典
        """
        self.templates = templates
        self.field_metadata = self.templates.get('proportion_field_metadata', {})
        self.proportion_templates = self.templates.get('proportion_templates', {})

    def analyze_field(self, field_name: str, stats: Dict[str, Any]) -> Optional[str]:
        """
        分析单个占比统计字段，返回基于模板的事实陈述

        Args:
            field_name: 字段名 (language 或 encoding)
            stats: 统计数据

        Returns:
            事实陈述文本
        """
        if not stats or stats.get('total_count', 0) == 0:
            return None

        proportions = stats.get('proportions', {})
        if not proportions:
            return None

        # 检查字段是否在支持的字段列表中
        if field_name not in self.field_metadata:
            return None

        return self._generate_insight_from_template(field_name, stats, proportions)

    def _generate_insight_from_template(self, field_name: str, stats: Dict[str, Any], proportions: Dict[str, Any]) -> str:
        """基于模板生成洞察文本"""
        total_count = stats.get('total_count', 0)
        type_count = len(proportions)

        # 获取类型列表和最高占比的类型
        types = list(proportions.keys())
        most_common = stats.get('most_common', [])

        if type_count == 1:
            # 只有一种类型，使用单一类型模板
            type_name = types[0]
            template = self.proportion_templates.get('single_type', {}).get(field_name, '')
            if template:
                return template.format(type=type_name)
            else:
                return f"样本全部为{type_name}类型。"
        else:
            # 多种类型，使用多类型模板
            type_list = "、".join(types[:3])  # 最多显示前3种
            if type_count > 3:
                type_list += "等"

            if most_common and len(most_common) >= 2:
                # 有主导类型的情况
                dominant_type = most_common[0]
                dominant_count = most_common[1]
                dominant_ratio = dominant_count / total_count if total_count > 0 else 0

                template = self.proportion_templates.get('multiple_types', {}).get(field_name, '')
                if template:
                    return template.format(
                        count=type_count,
                        types=type_list,
                        dominant_type=dominant_type,
                        dominant_ratio=dominant_ratio
                    )
                else:
                    return f"样本涉及{type_count}种类型如{type_list}，其中{dominant_type}占比{dominant_ratio:.1%}是比例最高的类型。"
            else:
                # 没有主导类型的情况
                template = self.proportion_templates.get('multiple_types_no_dominant', {}).get(field_name, '')
                if template:
                    return template.format(count=type_count, types=type_list)
                else:
                    return f"样本涉及{type_count}种类型如{type_list}。"
    

