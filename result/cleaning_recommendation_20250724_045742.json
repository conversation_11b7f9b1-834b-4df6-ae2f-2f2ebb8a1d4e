{"operators": [{"name": "移除特殊字符 remove_special_chars", "order": 1, "reason": "文本中存在噪声和潜在的无效字符，且有效字符比例偏低，移除特殊字符（包括HTML实体和控制字符）能提高文本质量和清洁度，减少噪声对后续处理的干扰。", "parameters": [{"name": "keep_punctuation", "value": true, "description": "保留标点符号，避免影响文本语义结构"}, {"name": "custom_chars", "value": "", "description": "无自定义移除字符，默认清理特殊字符即可"}]}, {"name": "标准化空白字符 normalize_whitespace", "order": 2, "reason": "文本中空白字符（空格、制表符、换行符）分布不统一，存在多样化空白表现，标准化空白字符有助于格式统一，提升文本结构规范性和后续分析准确性。", "parameters": [{"name": "replace_tabs", "value": true, "description": "将制表符替换为空格，实现空白字符统一"}, {"name": "normalize_newlines", "value": true, "description": "标准化换行符，统一文本换行格式"}]}, {"name": "异常值移除 outlier_removal", "order": 3, "reason": "多个字段存在明显的异常样本（字符数、词数、句子数等）且分布右偏或左偏严重，移除异常值能提升整体数据质量，避免极端样本影响模型训练和分析。", "parameters": [{"name": "method", "value": "iqr", "description": "基于四分位距方法检测异常值，适合文本长度分布特征"}, {"name": "threshold", "value": 1.5, "description": "常用的IQR异常检测阈值，平衡异常识别和保留有效样本"}]}, {"name": "去重处理 remove_duplicates", "order": 4, "reason": "文本中存在较高的2-gram和3-gram重复率，表明有模板化或重复内容，去重处理能消除重复样本，提升数据多样性和训练效果。", "parameters": [{"name": "similarity_threshold", "value": 0.95, "description": "设定较高阈值确保去除高度重复样本"}, {"name": "method", "value": "fuzzy", "description": "模糊匹配方法适合检测文本重复，兼顾轻微差异"}]}, {"name": "敏感内容过滤 sensitive_content_filter", "order": 5, "reason": "数据中存在大量PII信息（邮箱、手机号、身份证号），敏感内容过滤可以有效脱敏，保护个人隐私，符合法规要求。", "parameters": [{"name": "action", "value": "mask", "description": "使用掩码替换敏感词，保留文本结构同时保护隐私"}, {"name": "mask_char", "value": "*", "description": "掩码字符设置为星号，常用且易识别"}]}, {"name": "文本标准化 text_normalization", "order": 6, "reason": "文本中存在格式不统一问题，标准化标点符号有助于统一文本格式，提高后续处理效果；不强制小写处理，避免影响中文语义。", "parameters": [{"name": "lowercase", "value": false, "description": "不转换为小写，保持中文文本原貌"}, {"name": "normalize_punctuation", "value": true, "description": "标准化标点符号，统一文本表达"}]}]}