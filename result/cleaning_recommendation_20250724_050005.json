{"operators": [{"name": "移除特殊字符 remove_special_chars", "order": 1, "reason": "数据中存在HTML标签及特殊字符，且有效字符比例偏低，噪声较多，移除特殊字符可提升文本清洁度，减少无效信息干扰。保留标点符号以维护文本结构。", "parameters": [{"name": "keep_punctuation", "value": true, "description": "保留标点符号，维护文本结构完整性"}, {"name": "custom_chars", "value": "", "description": "无自定义移除字符，默认移除特殊字符和控制字符"}]}, {"name": "标准化空白字符 normalize_whitespace", "order": 2, "reason": "文本存在换行符、制表符等空白字符不统一，标准化空白字符有助于后续文本处理和格式规范化，提升数据质量。", "parameters": [{"name": "replace_tabs", "value": true, "description": "将制表符替换为空格，统一空白字符"}, {"name": "normalize_newlines", "value": true, "description": "标准化换行符，规范文本格式"}]}, {"name": "异常值移除 outlier_removal", "order": 3, "reason": "样本中存在字符数、句子数、词数等多个字段的异常样本（极端短文本和极端长文本），移除异常样本可以提高整体数据质量和模型训练效果。", "parameters": [{"name": "method", "value": "iqr", "description": "采用IQR方法检测异常样本，依据统计分布过滤异常"}, {"name": "threshold", "value": 1.5, "description": "设置1.5倍IQR作为异常检测阈值"}]}, {"name": "去重处理 remove_duplicates", "order": 4, "reason": "文本存在较高的2-gram和3-gram重复度，可能含大量重复或模板内容，去重处理能提升文本多样性，减少冗余样本。", "parameters": [{"name": "similarity_threshold", "value": 0.95, "description": "高阈值确保严格去除高度相似文本"}, {"name": "method", "value": "fuzzy", "description": "模糊匹配方法更适合处理文本重复和模板内容"}]}, {"name": "敏感内容过滤 sensitive_content_filter", "order": 5, "reason": "敏感词检测存在误判（所有样本标记为无敏感词），需要过滤或脱敏敏感词汇以避免误判影响模型，采用掩码方式降低信息泄露风险。", "parameters": [{"name": "action", "value": "mask", "description": "采用掩码方式脱敏敏感词，防止信息泄露"}, {"name": "mask_char", "value": "*", "description": "掩码字符设置为星号"}]}]}