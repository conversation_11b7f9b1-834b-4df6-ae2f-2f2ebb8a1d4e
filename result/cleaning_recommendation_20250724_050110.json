{"operators": [{"name": "移除特殊字符 remove_special_chars", "order": 1, "reason": "数据中存在有效字符比例偏低，整体文本质量不佳，可能包含特殊字符、HTML实体和控制字符，移除这些特殊字符有助于提高文本质量和清洁度。", "parameters": [{"name": "keep_punctuation", "value": true, "description": "保留标点符号，避免破坏文本语义结构"}, {"name": "custom_chars", "value": "", "description": "默认移除常见特殊字符，无需额外自定义字符"}]}, {"name": "标准化空白字符 normalize_whitespace", "order": 2, "reason": "文本中存在空白字符不统一的问题，如制表符、换行符，统一空白字符格式有助于文本格式规范，提高后续处理准确性。", "parameters": [{"name": "replace_tabs", "value": true, "description": "将制表符替换为空格，统一空白字符"}, {"name": "normalize_newlines", "value": true, "description": "标准化换行符，保证文本格式一致"}]}, {"name": "异常值移除 outlier_removal", "order": 3, "reason": "系统字段和其他文本字段存在大量低字符数或极端长文本样本，且存在多条异常样本。移除异常样本可以提高数据整体质量和模型训练效果。", "parameters": [{"name": "method", "value": "iqr", "description": "基于四分位距法检测异常样本，适合本数据特点"}, {"name": "threshold", "value": 1.5, "description": "使用常用1.5倍IQR阈值，平衡异常检测效果"}]}, {"name": "长度过滤 filter_by_length", "order": 4, "reason": "存在大量极短文本（如系统和prompt字段中少于3字符样本）和极长文本（如response字段超过700字符样本），设置合理长度范围过滤无效和冗余文本，提高数据质量。", "parameters": [{"name": "min_length", "value": 10, "description": "过滤掉少于10字符的极短样本，避免无效文本"}, {"name": "max_length", "value": 1000, "description": "过滤掉超过1000字符的极长样本，避免冗余过多"}, {"name": "unit", "value": "chars", "description": "以字符数为单位进行长度过滤"}]}, {"name": "去重处理 remove_duplicates", "order": 5, "reason": "文本中2-gram和3-gram重复度普遍较高，存在模板文本和重复内容，去重处理有助于提升数据多样性和模型泛化能力。", "parameters": [{"name": "similarity_threshold", "value": 0.95, "description": "使用较高相似度阈值，精准去除重复文本"}, {"name": "method", "value": "fuzzy", "description": "模糊匹配方法，更有效识别近似重复内容"}]}, {"name": "敏感内容过滤 sensitive_content_filter", "order": 6, "reason": "数据集中涉及大量个人隐私信息（邮箱、手机号码、身份证号）和敏感词汇，且匹配覆盖全面但需保护隐私，采用掩码方式脱敏敏感信息，确保数据安全合规。", "parameters": [{"name": "action", "value": "mask", "description": "使用掩码方式保护敏感信息"}, {"name": "mask_char", "value": "*", "description": "使用星号作为掩码字符"}]}]}