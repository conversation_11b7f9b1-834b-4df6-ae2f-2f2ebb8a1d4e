#!/usr/bin/env python3
"""
数据分析流水线使用示例

演示如何使用优化后的main.py进行完整的数据分析流程
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import DataAnalysisPipeline
from utils.logger_util import logger


def run_example():
    """运行示例分析"""
    
    # 示例配置
    data_file = "data/test/test_data_fine_tuning_50_samples.jsonl"
    clean_keys = ["system", "prompt"]  # 根据实际数据字段调整
    
    logger.info("=" * 60)
    logger.info("开始运行数据分析流水线示例")
    logger.info("=" * 60)
    
    try:
        # 创建分析流水线
        pipeline = DataAnalysisPipeline(
            data_file=data_file,
            clean_keys=clean_keys,
            report_dir="report",
            result_dir="result"
        )
        
        # 运行完整分析流程
        results = pipeline.run_analysis()
        
        logger.info("=" * 60)
        logger.info("分析完成！生成的报告文件:")
        logger.info("=" * 60)
        
        for report_type, path in results.items():
            if report_type.endswith('_path'):
                logger.info(f"{report_type}: {path}")
        
        return results
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        raise


if __name__ == "__main__":
    run_example()
