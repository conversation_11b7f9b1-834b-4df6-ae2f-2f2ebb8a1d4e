#!/usr/bin/env python3
"""
测试洞察核心模块的错误处理功能

验证当遇到未配置字段时，是否正确记录警告并继续处理
"""

import sys
import os
import logging
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from operators.core.insight.insight_core import DataInsightCore


def test_insight_core_error_handling():
    """测试洞察核心模块的错误处理"""
    print("测试洞察核心模块的错误处理...")
    
    # 设置日志级别以便看到警告信息
    logging.basicConfig(level=logging.WARNING, format='%(levelname)s: %(message)s')
    
    # 创建洞察分析器
    insight_core = DataInsightCore()
    
    # 创建测试数据，包含未配置的字段
    profile_report = {
        'boxplot_stats': {
            'text_field': {
                'unknown_stat_field': {  # 这是一个未配置的字段
                    'count': 1000,
                    'median': 100,
                    'cv': 0.3
                },
                'char_count': {  # 这是一个已配置的字段（如果存在配置）
                    'count': 1000,
                    'median': 100,
                    'cv': 0.3
                }
            }
        },
        'anomaly_stats': {
            'text_field': {
                'unknown_anomaly_field': {  # 这是一个未配置的字段
                    'anomaly_sample_proportion': 0.1,
                    'median_anomaly_ratio': 0.02,
                    'max_anomaly_ratio': 0.05
                },
                'special_ratio': {  # 这是一个已配置的字段
                    'anomaly_sample_proportion': 0.1,
                    'median_anomaly_ratio': 0.02,
                    'max_anomaly_ratio': 0.05
                }
            }
        },
        'proportion_stats': {
            'unknown_proportion_field': {  # 这是一个未配置的字段
                'total_count': 1000,
                'proportions': {
                    'type1': {'count': 800, 'proportion': 0.8},
                    'type2': {'count': 200, 'proportion': 0.2}
                }
            },
            'language': {  # 这是一个已配置的字段
                'total_count': 1000,
                'proportions': {
                    'zh': {'count': 800, 'proportion': 0.8},
                    'en': {'count': 200, 'proportion': 0.2}
                }
            }
        }
    }
    
    print("\n开始处理包含未配置字段的数据...")
    print("预期会看到一些警告信息，但处理应该继续进行\n")
    
    try:
        # 应用洞察分析
        result = insight_core.apply_insights(profile_report)
        
        print("✓ 洞察分析完成，没有抛出异常")
        
        # 检查结果
        print("\n检查处理结果:")
        
        # 检查箱图统计
        boxplot_stats = result.get('boxplot_stats', {})
        for clean_field, field_stats in boxplot_stats.items():
            if isinstance(field_stats, dict):
                for stat_field, stats in field_stats.items():
                    if isinstance(stats, dict):
                        insight = stats.get('insight', 'NO_INSIGHT')
                        print(f"  箱图字段 {stat_field}: insight = '{insight}'")
        
        # 检查异常检测统计
        anomaly_stats = result.get('anomaly_stats', {})
        for clean_field, field_stats in anomaly_stats.items():
            if isinstance(field_stats, dict):
                for anomaly_field, stats in field_stats.items():
                    if isinstance(stats, dict):
                        insight = stats.get('insight', 'NO_INSIGHT')
                        print(f"  异常检测字段 {anomaly_field}: insight = '{insight}'")
        
        # 检查占比统计
        proportion_stats = result.get('proportion_stats', {})
        for field_name, stats in proportion_stats.items():
            if isinstance(stats, dict):
                insight = stats.get('insight', 'NO_INSIGHT')
                print(f"  占比字段 {field_name}: insight = '{insight}'")
        
        print("\n✓ 所有字段都有insight字段，错误处理正常工作")
        
    except Exception as e:
        print(f"✗ 洞察分析失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    print("开始测试洞察核心模块的错误处理功能...\n")
    
    success = test_insight_core_error_handling()
    
    if success:
        print("\n✓ 测试通过！错误处理功能正常工作")
    else:
        print("\n✗ 测试失败！")
