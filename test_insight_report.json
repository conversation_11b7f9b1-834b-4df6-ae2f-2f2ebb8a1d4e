{"summary": {"total_samples": 3, "analysis_timestamp": "2025-07-27T22:21:56.876690", "analyzed_fields": ["text", "content", "description"], "language_distribution": {"total_count": 3, "proportions": {"zh": {"count": 3, "proportion": 1.0}}, "most_common": ["zh", 3], "insight": "language字段(总样本3，1个类别)存在以下问题：language字段分布过度集中于'zh'类别(100.00%)建议：增加数据来源多样性。"}, "avg_text_length": 29.0, "content_diversity": 0, "domain_insights": "基于样本文本及模式匹配结果，该数据集主要包含文本中敏感词信息，且零散包含邮箱、手机号码及网址链接，属于文本内容安全与隐私保护领域，适用于敏感信息检测和内容审核相关的自然语言处理任务训练。建议：增加更多样本以提升邮箱、手机号码及网址等敏感信息的覆盖度，完善多样化的敏感词库，提升数据的代表性和泛化能力，同时优化正则表达式以减少潜在误匹配，确保数据质量与模型训练效果。"}, "boxplot_stats": {"text": {"char_count": {"count": 3, "mean": 27.6667, "std": 12.9701, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 32.5, "max": 46.0, "iqr": 14.0, "lower_bound": -2.5, "upper_bound": 53.5, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字符数(反映样本信息密度和完整性)：字符数分布极度右偏，存在极端长样本严重拉高分布，可能有严重的数据合并错误"}, "alpha_ratio": {"count": 3, "mean": 0.7974, "std": 0.1335, "min": 0.6087, "q1": 0.7488, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.143, "lower_bound": 0.5343, "upper_bound": 1.1063, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母占比(反映文本中字母字符的占比分布)：字母占比分布极度左偏，存在极端低字母占比样本，可能包含大量非字母内容"}, "digit_ratio": {"count": 3, "mean": 0.0797, "std": 0.1127, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.1196, "max": 0.2391, "iqr": 0.1196, "lower_bound": -0.1793, "upper_bound": 0.2989, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度较大，可能混合了数据表格和纯文本内容；数字占比分布极度右偏，存在极端高数字占比样本，可能混合了纯数字数据"}, "alnum_ratio": {"count": 3, "mean": 0.8772, "std": 0.0209, "min": 0.8478, "q1": 0.8684, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.0235, "lower_bound": 0.8332, "upper_bound": 0.927, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "valid_ratio": {"count": 3, "mean": 0.8772, "std": 0.0209, "min": 0.8478, "q1": 0.8684, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.0235, "lower_bound": 0.8332, "upper_bound": 0.927, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "word_count": {"count": 3, "mean": 13.0, "std": 4.2426, "min": 10.0, "q1": 10.0, "median": 10.0, "q3": 14.5, "max": 19.0, "iqr": 4.5, "lower_bound": 3.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数分布极度右偏，存在极端高词数样本严重拉高分布，数据来源可能严重混乱"}, "stopword_count": {"count": 3, "mean": 0.6667, "std": 0.4714, "min": 0.0, "q1": 0.5, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.5, "lower_bound": -0.25, "upper_bound": 1.75, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "停用词数(反映文本中功能词的使用频率)：数据变化幅度适中，样本分布相对合理；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 3, "mean": 1.3333, "std": 0.4714, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.5, "max": 2.0, "iqr": 0.5, "lower_bound": 0.25, "upper_bound": 2.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据分布极度右偏，存在极端高值样本严重拉高分布"}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 3, "mean": 20.0, "std": 2.1602, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 21.0, "max": 23.0, "iqr": 2.5, "lower_bound": 14.75, "upper_bound": 24.75, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_line_length": {"count": 3, "mean": 27.6667, "std": 12.9701, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 32.5, "max": 46.0, "iqr": 14.0, "lower_bound": -2.5, "upper_bound": 53.5, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据分布极度右偏，存在极端高值样本严重拉高分布"}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}, "content": {"char_count": {"count": 3, "mean": 43.3333, "std": 5.2493, "min": 36.0, "q1": 41.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 6.0, "lower_bound": 32.0, "upper_bound": 56.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字符数(反映样本信息密度和完整性)：字符数分布极度左偏，存在极端短样本严重拉低分布，可能有大量空白或无效数据"}, "alpha_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母占比(反映文本中字母字符的占比分布)：字母占比变化幅度很小，字符构成高度一致；字母占比分布极度左偏，存在极端低字母占比样本，可能包含大量非字母内容"}, "digit_ratio": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "alnum_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "valid_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "有效字符比例(反映文本清洁度和噪声水平)：有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 3, "mean": 14.3333, "std": 0.4714, "min": 14.0, "q1": 14.0, "median": 14.0, "q3": 14.5, "max": 15.0, "iqr": 0.5, "lower_bound": 13.25, "upper_bound": 15.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数变化幅度很小，样本信息量高度一致；词数分布极度右偏，存在极端高词数样本严重拉高分布，数据来源可能严重混乱"}, "stopword_count": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 3, "mean": 1.3333, "std": 0.4714, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.5, "max": 2.0, "iqr": 0.5, "lower_bound": 0.25, "upper_bound": 2.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据分布极度右偏，存在极端高值样本严重拉高分布"}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 3, "mean": 37.3333, "std": 13.6951, "min": 18.0, "q1": 32.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 15.0, "lower_bound": 9.5, "upper_bound": 69.5, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：句子长度普遍超过正常范围，可能存在断句失败或长复合句过多"}, "avg_line_length": {"count": 3, "mean": 43.3333, "std": 5.2493, "min": 36.0, "q1": 41.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 6.0, "lower_bound": 32.0, "upper_bound": 56.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据分布极度左偏，存在极端低值样本严重拉低分布"}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}, "description": {"char_count": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字符数(反映样本信息密度和完整性)：字符数分布极度左偏，存在极端短样本严重拉低分布，可能有大量空白或无效数据"}, "alpha_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母占比(反映文本中字母字符的占比分布)：字母占比变化幅度很小，字符构成高度一致；字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "alnum_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大；数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "word_count": {"count": 3, "mean": 8.6667, "std": 1.2472, "min": 7.0, "q1": 8.0, "median": 9.0, "q3": 9.5, "max": 10.0, "iqr": 1.5, "lower_bound": 5.75, "upper_bound": 11.75, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数分布明显左偏，存在较多低词数样本，可能有大量标题、标签或不完整文本"}, "stopword_count": {"count": 3, "mean": 0.6667, "std": 0.4714, "min": 0.0, "q1": 0.5, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.5, "lower_bound": -0.25, "upper_bound": 1.75, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "停用词数(反映文本中功能词的使用频率)：数据变化幅度适中，样本分布相对合理；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据变化幅度很小，样本差异不大"}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_line_length": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据分布极度左偏，存在极端低值样本严重拉低分布"}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}}, "anomaly_stats": {"text": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "content": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "description": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}}, "proportion_stats": {"language": {"total_count": 3, "proportions": {"zh": {"count": 3, "proportion": 1.0}}, "most_common": ["zh", 3], "insight": "language字段(总样本3，1个类别)存在以下问题：language字段分布过度集中于'zh'类别(100.00%)建议：增加数据来源多样性。"}, "encoding": {"total_count": 3, "proportions": {"utf-8": {"count": 3, "proportion": 1.0}}, "most_common": ["utf-8", 3], "insight": "encoding字段(总样本3，1个类别)存在以下问题：encoding字段分布过度集中于'utf-8'类别(100.00%)建议：增加数据来源多样性。"}}, "pattern_stats": {"text": {"pii_email": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["<EMAIL>", 1]], "insight": "text字段中邮箱地址匹配样本数仅为1，匹配质量较低，可能因样本有限或数据中邮箱出现频率低，真实存在数据稀疏问题。"}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["13812345678", 1]], "insight": "text字段中手机号码匹配样本数为1，匹配比例较低，说明手机号码数据覆盖不足，存在数据质量不完整风险。"}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "text字段中未检测到身份证号，说明该字段中身份证信息较少或无此类数据，匹配结果符合预期，无误匹配。"}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "text字段中未检测到姓名，表明姓名数据缺失或样本中无此类信息，数据质量正常，暂无误匹配。"}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "text字段中未检测到HTML标签，说明文本较为纯净，无HTML标签干扰，数据质量较好。"}, "urls": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["example.com", 1]], "insight": "text字段中网址链接匹配样本数为1，匹配比例较低，网址数据覆盖有限，存在数据不完整问题。"}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]], "insight": "text字段中敏感词汇匹配比例为100%，说明敏感词覆盖完整，匹配准确，无误匹配，但需关注敏感词定义范围。"}}, "content": {"pii_email": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到邮箱地址，说明该字段中邮箱信息缺失或频率极低，数据质量一般，无误匹配。"}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到手机号码，表明该字段缺少手机号码信息，数据完整性有待提升。"}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到身份证号，符合预期，无误匹配，数据质量正常。"}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到姓名，说明无此类数据，或数据覆盖不足，质量较为稳定。"}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到HTML标签，文本内容干净，无格式标签干扰，数据质量良好。"}, "urls": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "content字段中未检测到网址链接，可能因样本中无此类数据，数据质量整体正常。"}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]], "insight": "content字段中敏感词汇匹配比例为100%，说明敏感词检测准确且覆盖全面，数据质量高。"}}, "description": {"pii_email": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到邮箱地址，符合数据特性，无误匹配，数据质量稳定。"}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到手机号码，反映该字段手机号码数据缺失，存在数据不完整问题。"}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到身份证号，符合预期，无误匹配，数据质量正常。"}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到姓名，说明该字段无姓名信息，数据质量尚可。"}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到HTML标签，说明文本纯净，无格式标签影响，数据质量良好。"}, "urls": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "description字段中未检测到网址链接，数据覆盖不足，需关注样本多样性提升。"}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]], "insight": "description字段中敏感词汇匹配比例为100%，敏感词检测准确且覆盖充分，数据质量优良。"}}}, "insight_metadata": {"analysis_timestamp": "2025-07-27T22:22:14.929480", "analyzer_version": "2.0.0", "insights_embedded": true}}