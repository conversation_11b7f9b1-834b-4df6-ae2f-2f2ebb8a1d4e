#!/usr/bin/env python3
"""
测试logger功能的脚本
"""

from utils.logger_util import logger

def test_logger():
    """测试logger的各种日志级别"""
    
    # 测试不同级别的日志
    logger.info("[test_logger] 任务（测试）：这是一条信息日志")
    logger.warning("[test_logger] 任务（测试）：这是一条警告日志")
    logger.error("[test_logger] 任务（测试）：这是一条错误日志")
    logger.debug("[test_logger] 任务（测试）：这是一条调试日志")
    
    # 测试带变量的日志
    task_id = "test_001"
    script_name = "test_logger"
    count = 42
    
    logger.info(f"[{script_name}] 任务（{task_id}）：处理了 {count} 个项目")
    
    # 测试异常日志
    try:
        result = 1 / 0
    except Exception as e:
        logger.error(f"[{script_name}] 任务（{task_id}）：发生异常: {e}")
    
    logger.info(f"[{script_name}] 任务（{task_id}）：测试完成")

if __name__ == "__main__":
    test_logger()
