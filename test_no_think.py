#!/usr/bin/env python3
"""
测试no_think功能的脚本
"""

from operators.utils.llm_client import LLMClient

def test_remove_think_tags():
    """测试去除思考标签的功能"""
    client = LLMClient()
    
    # 测试包含思考标签的文本
    test_text = """这是一个测试。

<think>
这是思考部分，应该被去除。
可能包含多行内容。
</think>

这是正常的响应内容。

<think>
另一个思考部分
</think>

最终的结果。"""
    
    cleaned_text = client._remove_think_tags(test_text)
    print("原始文本:")
    print(test_text)
    print("\n" + "="*50 + "\n")
    print("清理后的文本:")
    print(cleaned_text)
    
    # 测试JSON响应中的思考标签
    json_text = """<think>
我需要生成一个JSON响应
</think>

{
    "result": "success",
    "data": {
        "message": "这是测试数据"
    }
}"""
    
    print("\n" + "="*50 + "\n")
    print("JSON测试 - 原始文本:")
    print(json_text)
    
    cleaned_json = client._remove_think_tags(json_text)
    print("\nJSON测试 - 清理后的文本:")
    print(cleaned_json)
    
    # 测试解析JSON
    parsed_json = client.parse_json_response(json_text)
    print("\n解析后的JSON:")
    print(parsed_json)

def test_no_think_prompt():
    """测试no_think参数对prompt的影响"""
    client = LLMClient()
    
    original_prompt = "请回答这个问题"
    
    print(f"no_think设置: {client.no_think}")
    print(f"原始prompt: {original_prompt}")
    
    # 模拟处理prompt的逻辑
    processed_prompt = original_prompt
    if client.no_think:
        processed_prompt = original_prompt + "/no_think"
    
    print(f"处理后的prompt: {processed_prompt}")

if __name__ == "__main__":
    print("测试no_think功能")
    print("="*50)
    
    test_no_think_prompt()
    print("\n" + "="*50 + "\n")
    test_remove_think_tags()
