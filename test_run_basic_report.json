{"summary": {"total_samples": 3, "analysis_timestamp": "2025-07-27T22:22:15.754646", "analyzed_fields": ["text", "content", "description"], "language_distribution": {"total_count": 3, "proportions": {"zh": {"count": 3, "proportion": 1.0}}, "most_common": ["zh", 3]}, "avg_text_length": 29.0, "content_diversity": 0}, "boxplot_stats": {"text": {"char_count": {"count": 3, "mean": 27.6667, "std": 12.9701, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 32.5, "max": 46.0, "iqr": 14.0, "lower_bound": -2.5, "upper_bound": 53.5, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alpha_ratio": {"count": 3, "mean": 0.7974, "std": 0.1335, "min": 0.6087, "q1": 0.7488, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.143, "lower_bound": 0.5343, "upper_bound": 1.1063, "lower_outlier_count": 0, "upper_outlier_count": 0}, "digit_ratio": {"count": 3, "mean": 0.0797, "std": 0.1127, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.1196, "max": 0.2391, "iqr": 0.1196, "lower_bound": -0.1793, "upper_bound": 0.2989, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alnum_ratio": {"count": 3, "mean": 0.8772, "std": 0.0209, "min": 0.8478, "q1": 0.8684, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.0235, "lower_bound": 0.8332, "upper_bound": 0.927, "lower_outlier_count": 0, "upper_outlier_count": 0}, "valid_ratio": {"count": 3, "mean": 0.8772, "std": 0.0209, "min": 0.8478, "q1": 0.8684, "median": 0.8889, "q3": 0.8918, "max": 0.8947, "iqr": 0.0235, "lower_bound": 0.8332, "upper_bound": 0.927, "lower_outlier_count": 0, "upper_outlier_count": 0}, "word_count": {"count": 3, "mean": 13.0, "std": 4.2426, "min": 10.0, "q1": 10.0, "median": 10.0, "q3": 14.5, "max": 19.0, "iqr": 4.5, "lower_bound": 3.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "stopword_count": {"count": 3, "mean": 0.6667, "std": 0.4714, "min": 0.0, "q1": 0.5, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.5, "lower_bound": -0.25, "upper_bound": 1.75, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "sentence_count": {"count": 3, "mean": 1.3333, "std": 0.4714, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.5, "max": 2.0, "iqr": 0.5, "lower_bound": 0.25, "upper_bound": 2.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_sentence_length": {"count": 3, "mean": 20.0, "std": 2.1602, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 21.0, "max": 23.0, "iqr": 2.5, "lower_bound": 14.75, "upper_bound": 24.75, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_line_length": {"count": 3, "mean": 27.6667, "std": 12.9701, "min": 18.0, "q1": 18.5, "median": 19.0, "q3": 32.5, "max": 46.0, "iqr": 14.0, "lower_bound": -2.5, "upper_bound": 53.5, "lower_outlier_count": 0, "upper_outlier_count": 0}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}}, "content": {"char_count": {"count": 3, "mean": 43.3333, "std": 5.2493, "min": 36.0, "q1": 41.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 6.0, "lower_bound": 32.0, "upper_bound": 56.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alpha_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0}, "digit_ratio": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alnum_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0}, "valid_ratio": {"count": 3, "mean": 0.8266, "std": 0.0346, "min": 0.7778, "q1": 0.8128, "median": 0.8478, "q3": 0.851, "max": 0.8542, "iqr": 0.0382, "lower_bound": 0.7555, "upper_bound": 0.9083, "lower_outlier_count": 0, "upper_outlier_count": 0}, "word_count": {"count": 3, "mean": 14.3333, "std": 0.4714, "min": 14.0, "q1": 14.0, "median": 14.0, "q3": 14.5, "max": 15.0, "iqr": 0.5, "lower_bound": 13.25, "upper_bound": 15.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "stopword_count": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "sentence_count": {"count": 3, "mean": 1.3333, "std": 0.4714, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.5, "max": 2.0, "iqr": 0.5, "lower_bound": 0.25, "upper_bound": 2.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_sentence_length": {"count": 3, "mean": 37.3333, "std": 13.6951, "min": 18.0, "q1": 32.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 15.0, "lower_bound": 9.5, "upper_bound": 69.5, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_line_length": {"count": 3, "mean": 43.3333, "std": 5.2493, "min": 36.0, "q1": 41.0, "median": 46.0, "q3": 47.0, "max": 48.0, "iqr": 6.0, "lower_bound": 32.0, "upper_bound": 56.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}}, "description": {"char_count": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alpha_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0}, "digit_ratio": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "alnum_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0}, "valid_ratio": {"count": 3, "mean": 0.9177, "std": 0.0217, "min": 0.8889, "q1": 0.906, "median": 0.9231, "q3": 0.9321, "max": 0.9412, "iqr": 0.0261, "lower_bound": 0.8668, "upper_bound": 0.9713, "lower_outlier_count": 0, "upper_outlier_count": 0}, "word_count": {"count": 3, "mean": 8.6667, "std": 1.2472, "min": 7.0, "q1": 8.0, "median": 9.0, "q3": 9.5, "max": 10.0, "iqr": 1.5, "lower_bound": 5.75, "upper_bound": 11.75, "lower_outlier_count": 0, "upper_outlier_count": 0}, "stopword_count": {"count": 3, "mean": 0.6667, "std": 0.4714, "min": 0.0, "q1": 0.5, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.5, "lower_bound": -0.25, "upper_bound": 1.75, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "sentence_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "line_count": {"count": 3, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_sentence_length": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "avg_line_length": {"count": 3, "mean": 16.0, "std": 2.1602, "min": 13.0, "q1": 15.0, "median": 17.0, "q3": 17.5, "max": 18.0, "iqr": 2.5, "lower_bound": 11.25, "upper_bound": 21.25, "lower_outlier_count": 0, "upper_outlier_count": 0}, "bigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "trigram_repetition": {"count": 3, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0}}}, "anomaly_stats": {"text": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}}, "content": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}}, "description": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0}}}, "proportion_stats": {"language": {"total_count": 3, "proportions": {"zh": {"count": 3, "proportion": 1.0}}, "most_common": ["zh", 3]}, "encoding": {"total_count": 3, "proportions": {"utf-8": {"count": 3, "proportion": 1.0}}, "most_common": ["utf-8", 3]}}, "pattern_stats": {"text": {"pii_email": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["<EMAIL>", 1]]}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["13812345678", 1]]}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "urls": {"total_samples": 3, "samples_with_pattern": 1, "proportion": 0.3333, "most_common_matches": [["example.com", 1]]}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]]}}, "content": {"pii_email": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "urls": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]]}}, "description": {"pii_email": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_phone": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_id_card": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "pii_name": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "html_tags": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "urls": {"total_samples": 3, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": []}, "sensitive_words": {"total_samples": 3, "samples_with_pattern": 3, "proportion": 1.0, "by_type": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 3, "sample_count": 3, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 3]]}}}}