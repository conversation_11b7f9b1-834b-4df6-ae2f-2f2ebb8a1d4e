#!/usr/bin/env python3
"""
Elasticsearch客户端工具模块
用于上传和下载数据分析报告
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError, ConnectionError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局ES客户端变量
client = None


def conn_es(hosts: Optional[List[str]] = None, **kwargs) -> bool:
    """
    连接到Elasticsearch服务

    Args:
        hosts: ES主机列表，默认为 ['localhost:9200']
        **kwargs: 其他ES客户端配置参数

    Returns:
        bool: 连接成功返回True，否则返回False
    """
    global client

    if hosts is None:
        hosts = ['localhost:9200']

    # 默认配置
    default_config = {
        'hosts': hosts,
        'timeout': 30,
        'max_retries': 3,
        'retry_on_timeout': True
    }

    # 合并用户配置
    config = {**default_config, **kwargs}

    try:
        client = Elasticsearch(**config)
        # 测试连接
        if not client.ping():
            raise ConnectionError("无法连接到Elasticsearch服务")
        logger.info(f"成功连接到Elasticsearch: {hosts}")
        return True
    except Exception as e:
        logger.error(f"连接Elasticsearch失败: {e}")
        client = None
        return False

def upload_profiling_data(dataset_batch_id: str, report_data: Dict[str, Any],
                        index_name: str = "data_profiling_reports") -> bool:
    """
    上传数据分析报告到ES

    Args:
        dataset_batch_id: 数据集批次ID
        report_data: 完整的报告数据字典
        index_name: 索引名称

    Returns:
        bool: 上传成功返回True，否则返回False
    """
    global client

    if client is None:
        logger.error("ES客户端未连接，请先调用conn_es()方法")
        return False

    try:
        # 构建文档
        doc = {
            "dataset_batch_id": dataset_batch_id,
            "report_data": report_data,
            "created_at": datetime.now().isoformat()
        }

        # 使用dataset_batch_id作为文档ID，实现幂等性
        response = client.index(  # type: ignore
            index=index_name,
            id=dataset_batch_id,
            body=doc
        )

        logger.info(f"成功上传报告: {dataset_batch_id}, 结果: {response['result']}")
        return True

    except RequestError as e:
        logger.error(f"上传报告请求错误: {e}")
        return False
    except Exception as e:
        logger.error(f"上传报告时发生错误: {e}")
        return False


def download_profiling_data(dataset_batch_id: str,
                          index_name: str = "data_profiling_reports") -> Optional[Dict[str, Any]]:
    """
    从ES下载数据分析报告

    Args:
        dataset_batch_id: 数据集批次ID
        index_name: 索引名称

    Returns:
        Dict[str, Any]: 报告数据，如果不存在返回None
    """
    global client

    if client is None:
        logger.error("ES客户端未连接，请先调用conn_es()方法")
        return None

    try:
        response = client.get(  # type: ignore
            index=index_name,
            id=dataset_batch_id
        )

        # 返回完整的文档数据
        doc_data = response['_source']
        logger.info(f"成功下载报告: {dataset_batch_id}")

        return doc_data

    except NotFoundError:
        logger.warning(f"报告不存在: {dataset_batch_id}")
        return None
    except Exception as e:
        logger.error(f"下载报告时发生错误: {e}")
        return None

def count_batch_data() -> int:
    return 1