"""
日志工具模块

提供统一的日志记录功能，用于替换项目中的print语句。

使用示例:
    from utils.logger_util import logger

    logger.info("这是一条信息日志")
    logger.error("这是一条错误日志")
    logger.debug("调试信息")
"""

import logging
import sys
from pathlib import Path
from datetime import datetime

class MyLogger:
    """自定义Logger类，包装标准logging.Logger"""

    def __init__(self, logger: logging.Logger):
        """
        初始化自定义Logger

        Args:
            logger (logging.Logger): 标准的Logger实例
        """
        self._logger = logger

    def log(self, message: str, level: str = "info"):
        """
        自定义日志方法

        Args:
            message (str): 日志消息内容
            level (str): 日志级别，默认为"info"
                        可选值: "debug", "info", "warning", "error", "critical"
        """
        level = level.lower()

        if level == "debug":
            self._logger.debug(message)
        elif level == "info":
            self._logger.info(message)
        elif level == "warning":
            self._logger.warning(message)
        elif level == "error":
            self._logger.error(message)
        elif level == "critical":
            self._logger.critical(message)
        else:
            # 如果传入的级别不支持，默认使用info级别
            self._logger.info(f"[UNKNOWN_LEVEL:{level}] {message}")

    # 提供对原始logger方法的访问
    def debug(self, message: str):
        """调试级别日志"""
        self._logger.debug(message)

    def info(self, message: str):
        """信息级别日志"""
        self._logger.info(message)

    def warning(self, message: str):
        """警告级别日志"""
        self._logger.warning(message)

    def error(self, message: str):
        """错误级别日志"""
        self._logger.error(message)

    def critical(self, message: str):
        """严重错误级别日志"""
        self._logger.critical(message)

def _setup_logger():
    """设置全局logger"""
    # 创建标准logger
    std_logger = logging.getLogger("data_profiling")
    std_logger.setLevel(logging.INFO)

    # 避免重复添加handler
    if std_logger.handlers:
        return MyLogger(std_logger)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 控制台格式
    console_format = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_format)

    # 文件处理器
    log_dir = Path("logs")
    if not log_dir.exists():
        log_dir.mkdir(exist_ok=True)

    file_handler = logging.FileHandler(
        log_dir / f"data_profiling_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)

    # 文件格式
    file_format = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)

    # 添加处理器
    std_logger.addHandler(console_handler)
    std_logger.addHandler(file_handler)

    # 返回自定义Logger实例
    return MyLogger(std_logger)


# 全局logger实例，供其他脚本直接使用
logger = _setup_logger()